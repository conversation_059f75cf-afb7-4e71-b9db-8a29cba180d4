import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 0,
    paddingVertical: 0
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: 34
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
    position: "relative"
  },
  backButton: {
    width: 24,
    height: 24,
    position: "absolute",
    left: 24,
    zIndex: 1
  },
  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 20,
    marginBottom: 32
  },
  logo: {
    width: 166,
    height: 76
  },
  clubMLogoContainer: {
    alignItems: "center",
    marginBottom: 40
  },
  clubMLogo: {
    width: 280,
    height: 100
  },
  termsContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 32,
    gap: 12,
    alignSelf: "center"
  },
  termsIcon: {
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 1.07,
    paddingVertical: 1
  },
  termsText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    textAlign: "center",
    width: 208,
    textAlignVertical: "center"
  },
  termsChevron: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 8,
    paddingVertical: 5
  },
  socialMediaTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    alignSelf: "center",
    marginBottom: 32
  },
  socialMediaContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 16,
    marginBottom: 318,
    alignSelf: "center"
  },
  socialMediaButton: {
    backgroundColor: "rgba(255, 255, 255, 0.60)",
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.60)",
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    padding: 8
  },
  socialMediaIcon: {
    width: 24,
    height: 24
  },
  versionContainer: {
    alignItems: "center",
    marginHorizontal: 139,
    marginBottom: 20
  },
  versionText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  homeIndicator: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 121,
    paddingBottom: 8,
    minHeight: 34
  },
  homeIndicatorBar: {
    width: 134,
    height: 5,
    flexGrow: 1
  }
});

export default styles;
