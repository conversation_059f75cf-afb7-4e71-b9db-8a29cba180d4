import {StyleSheet} from "react-native";

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
    backgroundColor: "#202938",
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8
  },
  content: {
    flex: 1,
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: "#374151",
    paddingTop: 8
  },
  title: {
    color: "#F9FAFB",
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    width: 65
  },
  optionsGrid: {
    maxHeight: 80
  },
  optionsRow: {
    flexDirection: "row",
    gap: 6,
    marginBottom: 8,
    paddingHorizontal: 4
  },
  paymentOption: {
    borderWidth: 1,
    borderColor: "#F2F4F7",
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    flex: 1,
    minWidth: 143,
    marginHorizontal: 2
  },
  paymentOptionSelected: {
    borderWidth: 1.5,
    borderColor: "#36A67A"
  },
  paymentOptionContent: {
    flex: 1,
    alignItems: "center"
  },
  installmentText: {
    textAlign: "center",
    minHeight: 18,
    width: "100%"
  },
  installmentNumber: {
    color: "#F9FAFB",
    fontSize: 10,
    fontWeight: "600",
    lineHeight: 18
  },
  installmentOf: {
    color: "#F9FAFB",
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18
  },
  installmentAmount: {
    color: "#F9FAFB",
    fontSize: 10,
    fontWeight: "600",
    lineHeight: 18
  },
  paymentLabel: {
    color: "#36A67A",
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    minHeight: 18
  }
});

export default styles;
