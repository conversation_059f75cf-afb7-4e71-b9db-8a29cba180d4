import React from "react";
import {Text, View, ScrollView, TouchableOpacity, Alert} from "react-native";
import Screen<PERSON>ithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router} from "expo-router";
import CopyIcon from "../../components/icons/copy-icon";
import PdfIcon from "../../components/icons/pdf-icon";
// Usando estilos inline para manter compatibilidade

const PixPayment: React.FC = () => {
  const pixCode = "5e2f9a0e-3b6c-4b97-a8f9-1c3f7e29b1f2";
  const totalValue = "R$ 150,00";

  const copyPixCode = async () => {
    try {
      // Implementação nativa para copiar texto
      // Em um ambiente real, você usaria Clipboard do React Native
      // Para este exemplo, vamos simular a funcionalidade

      // Mock da funcionalidade de copiar - em produção usar:
      // import { Clipboard } from 'react-native';
      // await Clipboard.setString(pixCode);

      Alert.alert(
        "Código copiado!",
        "O código foi copiado para a área de transferência."
      );
    } catch (error) {
      console.error("Erro ao copiar código:", error);
      Alert.alert("Erro", "Não foi possível copiar o código. Tente novamente.");
    }
  };

  const handleFinishPayment = () => {
    router.push("/(events)/payment-success");
  };

  return (
    <ScreenWithHeader screenTitle="Código de pagamento via Boleto" backButton>
      <ScrollView
        style={{flex: 1, paddingHorizontal: 24}}
        showsVerticalScrollIndicator={false}
      >
        {/* Container principal do conteúdo */}
        <View
          style={{
            backgroundColor: "#202938",
            borderRadius: 8,
            paddingHorizontal: 16,
            paddingTop: 20,
            paddingBottom: 24,
            marginBottom: 24,
            borderWidth: 1,
            borderColor: "#282A2E",
            shadowColor: "#374151",
            shadowOffset: {width: 0, height: 1},
            shadowOpacity: 0.08,
            shadowRadius: 2,
            elevation: 1
          }}
        >
          {/* Header */}
          <View style={{alignItems: "center", marginBottom: 8}}>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 18,
                fontWeight: "600",
                lineHeight: 28,
                textAlign: "center",
                marginBottom: 8
              }}
            >
              Efetuar pagamento
            </Text>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                textAlign: "center"
              }}
            >
              Confira as instruções e efetue o pagamento.
            </Text>
          </View>

          {/* Valor total */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              gap: 8,
              marginTop: 16,
              marginBottom: 24
            }}
          >
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 16,
                lineHeight: 24,
                fontWeight: "400"
              }}
            >
              Valor total
            </Text>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 16,
                lineHeight: 24,
                fontWeight: "700"
              }}
            >
              {totalValue}
            </Text>
          </View>

          {/* Código PIX */}
          <View style={{marginBottom: 4}}>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                textAlign: "center",
                marginBottom: 8
              }}
            >
              Digite / copie o código manualmente:
            </Text>

            <View
              style={{
                backgroundColor: "transparent",
                paddingHorizontal: 16,
                paddingVertical: 12,
                borderRadius: 8,
                borderWidth: 1,
                borderColor: "#EAECF0",
                height: 44,
                justifyContent: "center",
                alignItems: "center",
                marginBottom: 4,
                shadowColor: "#101828",
                shadowOffset: {width: 0, height: 1},
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1
              }}
            >
              <Text
                style={{
                  color: "#DFE9F0",
                  fontSize: 14,
                  lineHeight: 20,
                  textAlign: "center"
                }}
              >
                {pixCode}
              </Text>
            </View>

            <TouchableOpacity
              style={{
                backgroundColor: "#DFE9F0",
                borderRadius: 8,
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
                height: 40,
                marginBottom: 8,
                borderWidth: 1,
                borderColor: "#D0D5DD",
                shadowColor: "#101828",
                shadowOffset: {width: 0, height: 1},
                shadowOpacity: 0.05,
                shadowRadius: 2,
                elevation: 1
              }}
              onPress={copyPixCode}
            >
              <View style={{marginRight: 8}}>
                <CopyIcon width={20} height={20} color="#344054" />
              </View>
              <Text
                style={{
                  color: "#344054",
                  fontSize: 14,
                  fontWeight: "700",
                  lineHeight: 20
                }}
              >
                Copiar código
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                backgroundColor: "transparent",
                alignItems: "center",
                flexDirection: "row",
                justifyContent: "center",
                marginTop: 8,
                marginBottom: 8
              }}
              onPress={() => {
                // Implementar visualização do PDF
                Alert.alert(
                  "PDF",
                  "Funcionalidade de visualizar PDF será implementada"
                );
              }}
            >
              <View style={{marginRight: 8}}>
                <PdfIcon width={20} height={20} color="#FCFCFD" />
              </View>
              <Text
                style={{
                  color: "#FCFCFD",
                  fontSize: 14,
                  fontWeight: "600",
                  lineHeight: 20
                }}
              >
                Visualizar PDF do boleto
              </Text>
            </TouchableOpacity>

            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 12,
                lineHeight: 18,
                textAlign: "center",
                marginTop: 8
              }}
            >
              Clique na opção acima caso queira obter uma cópia física e pagar
              em caixa eletrônico ou uma lotérica de sua preferência.
            </Text>
          </View>

          {/* Instruções de pagamento */}
          <View style={{marginTop: 12}}>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 18,
                fontWeight: "700",
                lineHeight: 28,
                textAlign: "center",
                marginBottom: 8
              }}
            >
              Instruções de pagamento
            </Text>

            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                marginTop: 8,
                marginBottom: 8
              }}
            >
              <Text style={{fontWeight: "700"}}>1º passo:</Text> Digite ou copie
              o código que foi gerado no campo acima;
            </Text>

            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                marginTop: 8,
                marginBottom: 8
              }}
            >
              <Text style={{fontWeight: "700"}}>2º passo:</Text> Acesse sua
              plataforma de pagamento / internet banking;
            </Text>

            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                marginTop: 8,
                marginBottom: 8
              }}
            >
              <Text style={{fontWeight: "700"}}>3º passo:</Text> Cole o código,
              confirme o valor e efetue o pagamento;
            </Text>

            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                marginTop: 8
              }}
            >
              Prontinho! Em alguns minutos o status da sua dívida será
              atualizado em nosso sistema.
            </Text>
          </View>
        </View>

        {/* Divisão cinza */}
        <View
          style={{
            backgroundColor: "#111828",
            borderTopWidth: 1,
            borderTopColor: "#1D2939",
            paddingTop: 24
          }}
        />

        {/* Timer */}
        <View style={{alignItems: "center", marginBottom: 16, gap: 4}}>
          <View style={{flexDirection: "row", alignItems: "center", gap: 4}}>
            <Text
              style={{
                color: "#FCFCFD",
                fontSize: 14,
                lineHeight: 20
              }}
            >
              Você tem até o dia
            </Text>
            <Text
              style={{
                color: "#DFE9F0",
                fontSize: 14,
                lineHeight: 20,
                fontWeight: "700"
              }}
            >
              07/06
            </Text>
            <Text
              style={{
                color: "#FCFCFD",
                fontSize: 14,
                lineHeight: 20
              }}
            >
              para efetuar
            </Text>
          </View>
          <Text
            style={{
              color: "#FCFCFD",
              fontSize: 14,
              lineHeight: 20,
              textAlign: "center"
            }}
          >
            o pagamento do boleto antes do vencimento.
          </Text>
        </View>

        <FullSizeButton
          text="Finalizar pagamento"
          onPress={handleFinishPayment}
        />
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PixPayment;
