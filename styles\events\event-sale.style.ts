import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 100 // Space for bottom purchase bar
  },
  scrollContentContainer: {
    paddingBottom: 120 // Extra space for bottom purchase bar
  },
  // Header Styles
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center"
  },
  backButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 24,
    fontWeight: "300"
  },
  headerTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  headerRight: {
    position: "relative"
  },
  cartButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center"
  },
  cartBadge: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "#FF4444",
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center"
  },
  cartBadgeText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 10,
    fontWeight: "600"
  },
  // Event Card Styles
  eventContentContainer: {
    alignItems: "center",
    paddingHorizontal: 16
  },
  eventCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 16,
    padding: 24,
    marginHorizontal: 16,
    marginTop: 32,
    marginBottom: 20,
    alignItems: "center"
  },
  eventIconContainer: {
    width: 64,
    height: 64,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 20,
    // Ensure perfect centering of the icon
    display: "flex",
    alignSelf: "center"
  },
  eventTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 26,
    textAlign: "center",
    marginBottom: 16
  },
  eventDescriptionContainer: {
    marginBottom: 20,
    paddingHorizontal: 8
  },
  eventDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  seeMoreText: {
    color: "#ADA843",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  freeEventBadgeContainer: {
    alignItems: "center",
    marginBottom: 20
  },
  purchaseButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: "center",
    marginBottom: 20,
    width: "100%"
  },
  purchaseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  // Attendees Section
  attendeesSection: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 16,
    paddingHorizontal: 8
  },
  attendeesText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1
  },
  attendeesNumber: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },
  inviteButton: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  inviteButtonText: {
    color: "#374151",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },
  // Event Details Styles
  eventDetailsContainer: {
    // Card styling is now applied via styles.eventCard
    // Only keeping container-specific properties here
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 15,
    fontWeight: "600",
    lineHeight: 22,
    marginBottom: 16
  },
  eventDetailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
    minHeight: 20
  },
  eventDetailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 13,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1
  },
  eventDetailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 13,
    fontWeight: "500",
    lineHeight: 18,
    textAlign: "right",
    flex: 1,
    marginLeft: 12
  },
  eventTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end"
  },
  eventTypeIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: stylesConstants.colors.brand.primary,
    marginRight: 6
  },
  // Payment Methods Styles
  paymentMethodsContainer: {
    marginBottom: 20,
    paddingHorizontal: 8
  },
  paymentMethodsGrid: {
    flexDirection: "row",
    gap: 6,
    marginBottom: 8,
    flexWrap: "wrap"
  },
  paymentMethodIcon: {
    width: 36,
    height: 22,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 3,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  paymentMethodCard: {
    width: 40,
    height: 28,
    borderRadius: 4,
    backgroundColor: stylesConstants.colors.fullWhite
  },
  pixCard: {
    backgroundColor: "#32BCAD"
  },
  boletoCard: {
    backgroundColor: "#FF5E00"
  },
  visaCard: {
    backgroundColor: "#1F72CD"
  },
  mastercardCard: {
    backgroundColor: "#EB001B"
  },
  amexCard: {
    backgroundColor: "#006FCF"
  },
  paymentMethodText: {
    fontSize: 14
  },
  installmentText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 11,
    fontWeight: "400",
    lineHeight: 16,
    marginTop: 8
  },
  installmentLinkText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 11,
    fontWeight: "400",
    lineHeight: 16,
    textDecorationLine: "underline"
  },
  // Similar Events Styles
  similarEventsContainer: {
    marginBottom: 20,
    paddingHorizontal: 8
  },
  similarEventsScroll: {
    marginHorizontal: -8
  },
  similarEventsScrollContainer: {
    paddingHorizontal: 8,
    gap: 12
  },
  // Card-specific styles moved to components/similar-event-card.style.ts
  // Bottom Purchase Bar Styles
  bottomPurchaseBar: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
    paddingBottom: 16
  },
  cartIconButton: {
    width: 44,
    height: 44,
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  cartIconText: {
    fontSize: 18
  },
  bottomPurchaseButton: {
    flex: 1,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    justifyContent: "center",
    alignItems: "center"
  },
  bottomPurchaseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  }
});

export default styles;
