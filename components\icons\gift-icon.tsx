import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface GiftIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const GiftIcon: React.FC<GiftIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path
        d="M6.66667 14.6667H25.3333V24C25.3333 25.4728 24.1394 26.6667 22.6667 26.6667H9.33333C7.86057 26.6667 6.66667 25.4728 6.66667 24V14.6667Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M5.33333 10.6667C5.33333 9.19391 6.52724 8 8 8H24C25.4728 8 26.6667 9.19391 26.6667 10.6667V12C26.6667 13.4728 25.4728 14.6667 24 14.6667H8C6.52724 14.6667 5.33333 13.4728 5.33333 12V10.6667Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M16 8V26.6667"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M16 8C16 6.52724 14.8061 5.33333 13.3333 5.33333C11.8606 5.33333 10.6667 6.52724 10.6667 8C10.6667 9.47276 11.8606 10.6667 13.3333 10.6667C14.8061 10.6667 16 9.47276 16 8Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M16 8C16 6.52724 17.1939 5.33333 18.6667 5.33333C20.1394 5.33333 21.3333 6.52724 21.3333 8C21.3333 9.47276 20.1394 10.6667 18.6667 10.6667C17.1939 10.6667 16 9.47276 16 8Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default GiftIcon;
