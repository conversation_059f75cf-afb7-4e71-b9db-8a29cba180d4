import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert
} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useLocalSearchParams, router} from "expo-router";
import styles from "@/styles/events/event-cancellation.style";
import stylesConstants from "@/styles/styles-constants";

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  attendees: number;
  status: "active" | "cancelled";
  ticketsSold: number;
  revenue: number;
  price: number;
  organizer: string;
  type: string;
  description: string;
}

const EventCancellation: React.FC = () => {
  const {id} = useLocalSearchParams();

  // Screen state: 'form' or 'success'
  const [screenState, setScreenState] = useState<"form" | "success">("form");

  const [event] = useState<Event>({
    id: (id as string) || "1",
    title: "Encontro anual de empreendedores em Balneário Camboriú - SC",
    date: "14/05/2025",
    time: "19:30 PM - 21:30 PM",
    location: "Expocentro Balneário Camboriú",
    attendees: 500,
    status: "active",
    ticketsSold: 500,
    revenue: 93750.0,
    price: 187.5,
    organizer: "Sheep Digital Marketing",
    type: "Presencial",
    description:
      "Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum dignissim..."
  });

  const [selectedReason, setSelectedReason] = useState("");
  const [specificReason, setSpecificReason] = useState("");

  const cancellationReasons = [
    {
      id: "not-interested",
      text: "O evento não é mais do meu interesse"
    },
    {
      id: "last-minute",
      text: "Reunião de última hora"
    },
    {
      id: "personal",
      text: "Motivos pessoais"
    },
    {
      id: "schedule-conflict",
      text: "Conflito de agenda"
    },
    {
      id: "other-commitments",
      text: "Outros compromissos"
    },
    {
      id: "family-commitments",
      text: "Compromissos familiares"
    }
  ];

  const handleReasonSelect = (reasonId: string) => {
    setSelectedReason(reasonId);
  };

  const handleContinue = () => {
    if (!selectedReason) {
      Alert.alert("Erro", "Por favor, selecione um motivo para o cancelamento");
      return;
    }
    setScreenState("success");
  };

  const handleBack = () => {
    if (screenState === "success") {
      setScreenState("form");
    } else {
      router.back();
    }
  };

  const handleClose = () => {
    router.back();
  };

  const renderFormScreen = () => (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <Text style={styles.formTitle}>Desmarcar evento</Text>
          <Text style={styles.questionText}>
            Por qual motivo você está desmarcando sua presença deste evento?
          </Text>
        </View>

        <View style={styles.reasonButtonsContainer}>
          {cancellationReasons.map((reason) => (
            <TouchableOpacity
              key={reason.id}
              style={[
                styles.reasonButton,
                selectedReason === reason.id && styles.reasonButtonSelected
              ]}
              onPress={() => handleReasonSelect(reason.id)}
            >
              <Text
                style={[
                  styles.reasonButtonText,
                  selectedReason === reason.id &&
                    styles.reasonButtonTextSelected
                ]}
              >
                {reason.text}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.specificReasonContainer}>
          <Text style={styles.specificReasonLabel}>
            Algum motivo específico?
          </Text>
          <TextInput
            style={styles.textInput}
            placeholder="Descreva melhor o motivo aqui (opcional)."
            placeholderTextColor={stylesConstants.colors.textPrimary}
            multiline
            value={specificReason}
            onChangeText={setSpecificReason}
            maxLength={250}
          />
          <Text style={styles.characterCount}>
            {specificReason.length}/250 caracteres
          </Text>
        </View>

        <View style={styles.formButtonsContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleContinue}
          >
            <Text style={styles.primaryButtonText}>Continuar</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton} onPress={handleBack}>
            <Text style={styles.secondaryButtonText}>Voltar</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );

  const renderSuccessScreen = () => (
    <View style={styles.container}>
      <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
        <View style={styles.successHeader}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <View style={styles.successIcon}>
            <Text style={{color: "#fff", fontSize: 20}}>✓</Text>
          </View>

          <Text style={styles.successTitle}>
            Seu cancelamento do evento foi confirmado
          </Text>
          <Text style={styles.successDescription}>
            O valor do ingresso será reembolsado em até 7 dias úteis, conforme
            método de pagamento utilizado.
          </Text>
        </View>

        <View style={styles.eventContent}>
          <Text style={styles.eventTitle}>{event.title}</Text>

          <Text style={styles.eventDescription}>
            {event.description} <Text style={styles.seeMoreText}>Ver mais</Text>
          </Text>

          <TouchableOpacity style={styles.priceButton}>
            <Text style={styles.priceButtonText}>
              Adquira por: R$ {event.price.toFixed(2).replace(".", ",")}
            </Text>
          </TouchableOpacity>

          <View style={styles.attendeesInfo}>
            <Text style={styles.attendeesText}>
              <Text style={styles.attendeesNumber}>
                {event.attendees} pessoas
              </Text>{" "}
              marcaram presença nesse evento.
            </Text>
            <TouchableOpacity style={styles.inviteButton}>
              <Text style={styles.inviteButtonText}>Convidar membro(s)</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.eventDetailsContainer}>
            <Text style={styles.eventDetailsTitle}>Detalhes do evento</Text>

            <View style={styles.eventDetailRow}>
              <Text style={styles.eventDetailLabel}>Sediado por</Text>
              <Text style={styles.eventDetailValue}>{event.organizer}</Text>
            </View>

            <View style={styles.eventDetailRow}>
              <Text style={styles.eventDetailLabel}>Tipo de evento</Text>
              <View style={{flexDirection: "row", alignItems: "center"}}>
                <Text style={{fontSize: 16, marginRight: 8}}>👥</Text>
                <Text style={styles.eventDetailValue}>{event.type}</Text>
              </View>
            </View>

            <View style={styles.eventDetailRow}>
              <Text style={styles.eventDetailLabel}>Data do evento</Text>
              <Text style={styles.eventDetailValue}>{event.date}</Text>
            </View>

            <View style={styles.eventDetailRow}>
              <Text style={styles.eventDetailLabel}>Horários do evento</Text>
              <Text style={styles.eventDetailValue}>{event.time}</Text>
            </View>

            <View style={styles.eventDetailRow}>
              <Text style={styles.eventDetailLabel}>Local do evento</Text>
              <Text style={styles.eventDetailValue}>{event.location}</Text>
            </View>
          </View>

          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity style={styles.cartButton}>
              <Text style={{color: "#fff", fontSize: 20}}>🛒</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.purchaseButton}>
              <Text style={styles.purchaseButtonText}>
                Comprar agora por R$ {event.price.toFixed(2).replace(".", ",")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  if (screenState === "form") {
    return (
      <ScreenWithHeader screenTitle="Desmarcar evento" backButton>
        {renderFormScreen()}
      </ScreenWithHeader>
    );
  }

  return renderSuccessScreen();
};

export default EventCancellation;
