import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface VipAccessIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const VipAccessIcon: React.FC<VipAccessIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FFFFFF",
    [props.replaceColor]
  );

  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Path
        d="M16 20C20.4183 20 24 16.4183 24 12C24 7.58172 20.4183 4 16 4C11.5817 4 8 7.58172 8 12C8 16.4183 11.5817 20 16 20Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M16 16L18 12L16 8L14 12L16 16Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M4 28C4 24.6863 7.58172 22 12 22H20C24.4183 22 28 24.6863 28 28"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default VipAccessIcon;
