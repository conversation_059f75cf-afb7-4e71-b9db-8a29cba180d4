import React, {useState, useCallback} from "react";
import {Text, View, ColorValue} from "react-native";
import Screen from "@/components/screen";
import BackgroundLogoTexture from "@/components/logos/background-logo-texture";
import BackButton from "@/components/back-button";
import {useTranslation} from "react-i18next";
import FullSizeButton from "@/components/full-size-button";
import InputField from "@/components/input-field";
import PasswordIcon from "@/components/icons/password-icon";
import styles from "@/styles/auth/password-recovery/recovery-password-new.style";

const RecoveryPasswordNew: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
      // Clear error when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({...prev, [field]: ""}));
      }
    };

  const handleSubmit = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.newPassword) {
      newErrors.newPassword = t(
        "recoveryPasswordNew.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = t(
        "recoveryPasswordNew.errors.minLength",
        "Mínimo 6 caracteres"
      );
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "recoveryPasswordNew.errors.required",
        "Campo obrigatório"
      );
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = t(
        "recoveryPasswordNew.errors.mismatch",
        "Senhas não coincidem"
      );
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Handle password reset
    console.log("Reset password with:", formData.newPassword);
  };

  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  return (
    <>
      <BackgroundLogoTexture />
      <Screen>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <View>
              <BackButton style={styles.backButton} />
              <View style={styles.headerContainer}>
                <Text style={styles.title}>
                  {t("recoveryPasswordNew.title", "Nova Senha")}
                </Text>
                <Text style={styles.description}>
                  {t(
                    "recoveryPasswordNew.description",
                    "Crie uma nova senha para sua conta"
                  )}
                </Text>
              </View>
              <View style={styles.formContainer}>
                <View style={styles.inputContainer}>
                  <InputField
                    label={t("recoveryPasswordNew.newPassword", "Nova Senha")}
                    value={formData.newPassword}
                    onChangeText={handleInputChange("newPassword")}
                    placeholder={t(
                      "recoveryPasswordNew.newPasswordPlaceholder",
                      "Digite sua nova senha"
                    )}
                    icon={passwordIcon}
                    isPassword
                    error={errors.newPassword}
                  />
                  <InputField
                    label={t(
                      "recoveryPasswordNew.confirmPassword",
                      "Confirmar Senha"
                    )}
                    value={formData.confirmPassword}
                    onChangeText={handleInputChange("confirmPassword")}
                    placeholder={t(
                      "recoveryPasswordNew.confirmPasswordPlaceholder",
                      "Digite novamente sua senha"
                    )}
                    icon={passwordIcon}
                    isPassword
                    error={errors.confirmPassword}
                  />
                </View>
              </View>
            </View>
            <View style={styles.buttonContainer}>
              <FullSizeButton
                text={t("recoveryPasswordNew.confirm", "Confirmar Nova Senha")}
                onPress={handleSubmit}
              />
            </View>
          </View>
        </View>
      </Screen>
    </>
  );
};

export default RecoveryPasswordNew;
