import React from "react";
import {Text, View, ScrollView, TouchableOpacity, Image} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {router} from "expo-router";
import styles from "@/styles/events/credit-card-confirmation.style";

interface ProductItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

const CreditCardConfirmation: React.FC = () => {
  // Mock data to match the design
  const products: ProductItem[] = [
    {
      id: "1",
      name: "Mentoria Coletiva sobre Resoluções Jurídicas",
      price: 1580.0,
      quantity: 1
    },
    {
      id: "2",
      name: "Encontro - Comemoração de 10 anos da Cafeeira Garibaldi",
      price: 1200.0,
      quantity: 1
    }
  ];

  const totalValue = products.reduce(
    (sum, product) => sum + product.price * product.quantity,
    0
  );

  const handleChangePaymentMethod = () => {
    router.back();
  };

  const handleConfirmPayment = () => {
    router.push(
      `/(events)/payment-success?paymentType=credit-card&totalValue=${totalValue}`
    );
  };

  return (
    <ScreenWithHeader screenTitle="Revise os dados" backButton>
      <View style={styles.container}>
        <ScrollView style={{flex: 1}} showsVerticalScrollIndicator={false}>
          <View style={styles.contentContainer}>
            {/* Product List Section */}
            <View style={styles.sectionContainer}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Lista de produtos</Text>
                <Text style={styles.itemCountText}>
                  ({products.length} itens)
                </Text>
              </View>

              {products.map((product) => (
                <View key={product.id} style={styles.productCard}>
                  <View style={styles.productIconContainer}>
                    <Image
                      source={{
                        uri: "https://static.motiffcontent.com/private/resource/image/197e7173318c39f-922feb15-e41d-47ef-a817-320ec8e82e5b.svg"
                      }}
                      style={styles.productIcon}
                      resizeMode="contain"
                    />
                  </View>
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{product.name}</Text>
                    <Text style={styles.productPrice}>
                      R$ {product.price.toFixed(2).replace(".", ",")}
                    </Text>
                  </View>
                  <Text style={styles.productQuantity}>
                    {product.quantity}x
                  </Text>
                </View>
              ))}
            </View>

            {/* Payment Method Section */}
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Métodos de pagamento</Text>

              <View style={styles.paymentMethodContainer}>
                <Image
                  source={{
                    uri: "https://static.motiffcontent.com/private/resource/image/197e7173319012e-90fa86cd-f2a2-4633-aa85-9c78e2be213e.svg"
                  }}
                  style={styles.paymentMethodIcon}
                  resizeMode="contain"
                />
                <View style={styles.paymentMethodInfo}>
                  <View style={styles.paymentMethodHeader}>
                    <Text style={styles.paymentMethodType}>
                      Cartão Mastercard
                    </Text>
                    <Text style={styles.paymentMethodInstallments}>
                      (Parcelado em 3x sem juros)
                    </Text>
                  </View>
                  <View style={styles.paymentMethodDetails}>
                    <Text style={styles.paymentMethodCredit}>Crédito</Text>
                    <Text style={styles.paymentMethodCardNumber}>
                      (com final 9876)
                    </Text>
                    <Text style={styles.paymentMethodSeparator}>-</Text>
                    <Text style={styles.paymentMethodImmediate}>
                      Pagamento imediato
                    </Text>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                style={styles.changePaymentMethodContainer}
                onPress={handleChangePaymentMethod}
              >
                <Image
                  source={{
                    uri: "https://static.motiffcontent.com/private/resource/image/197e717331a8129-9c012662-1ac7-47a6-9a20-4f680633d7f5.svg"
                  }}
                  style={styles.changePaymentMethodIcon}
                  resizeMode="contain"
                />
                <Text style={styles.changePaymentMethodText}>
                  Selecionar outro método de pagamento
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>

        {/* Bottom Container with Total and Payment Button */}
        <View style={styles.bottomContainer}>
          <View style={styles.totalContainer}>
            <Text style={styles.totalLabel}>Valor a ser pago</Text>
            <Text style={styles.totalValue}>
              R$ {totalValue.toFixed(2).replace(".", ",")}
            </Text>
          </View>

          <TouchableOpacity
            style={styles.paymentButton}
            onPress={handleConfirmPayment}
          >
            <Text style={styles.paymentButtonText}>Efetuar pagamento</Text>
          </TouchableOpacity>

          <View style={styles.homeIndicatorContainer}>
            <View style={styles.homeIndicator} />
          </View>
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default CreditCardConfirmation;
