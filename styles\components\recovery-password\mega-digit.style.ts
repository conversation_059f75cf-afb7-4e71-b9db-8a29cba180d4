import {StyleSheet} from "react-native";
import stylesConstants from "../../styles-constants";

const styles = StyleSheet.create({
  container: {
    display: "flex",
    width: 48,
    height: 48,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,
    borderWidth: 2,
    borderStyle: "solid",
    borderColor: stylesConstants.colors.gray300,
    backgroundColor: stylesConstants.colors.fullWhite
  },
  input: {
    fontSize: 24,
    fontStyle: "normal",
    fontWeight: 600,
    lineHeight: 24,
    width: "100%",
    textAlign: "center",
    fontFamily: stylesConstants.fonts.inter,
    color: stylesConstants.colors.brand.primary,
    paddingVertical: 0,
    paddingHorizontal: 0
  },
  onInputFillBorder: {
    borderColor: stylesConstants.colors.brand.primary,
    borderWidth: 2,
    borderStyle: "solid"
  }
});

export default styles;
