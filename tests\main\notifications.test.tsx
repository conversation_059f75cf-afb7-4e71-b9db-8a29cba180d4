import React from "react";
import {render, screen, fireEvent} from "@testing-library/react-native";
import Notifications from "../../app/(main)/notifications";

// Mock the translation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue: string) => defaultValue
  })
}));

// Mock the screen with header component
jest.mock("../../components/screen-with-header", () => {
  return function MockScreenWithHeader({children, screenTitle}: any) {
    return (
      <div testID="screen-with-header" data-title={screenTitle}>
        {children}
      </div>
    );
  };
});

// Mock the icons
jest.mock("../../components/icons/calendar-icon", () => {
  return function MockCalendarIcon() {
    return <div testID="calendar-icon" />;
  };
});

jest.mock("../../components/icons/announce-icon", () => {
  return function MockAnnounceIcon() {
    return <div testID="announce-icon" />;
  };
});

jest.mock("../../components/icons/general-notifications-icon", () => {
  return function MockGeneralNotificationsIcon() {
    return <div testID="general-notifications-icon" />;
  };
});

describe("Notifications", () => {
  it("renders correctly with default state", () => {
    render(<Notifications />);

    // Check if the screen with header is rendered
    expect(screen.getByTestId("screen-with-header")).toBeTruthy();

    // Check if all tab buttons are present
    expect(screen.getByText("Todas")).toBeTruthy();
    expect(screen.getByText("Lidas")).toBeTruthy();
    expect(screen.getByText("Não lidas")).toBeTruthy();
  });

  it("displays notifications correctly", () => {
    render(<Notifications />);

    // Check if notifications are displayed
    expect(screen.getByText(/Phoenix Baker/)).toBeTruthy();
    expect(screen.getByText(/Club M Alphaville/)).toBeTruthy();
    expect(screen.getByText("Ver evento")).toBeTruthy();
    expect(screen.getByText("Ver enquete")).toBeTruthy();
  });

  it("filters notifications correctly when tabs are pressed", () => {
    render(<Notifications />);

    // Initially should show all notifications
    expect(screen.getByText(/Phoenix Baker/)).toBeTruthy();
    expect(screen.getByText(/Club M Alphaville/)).toBeTruthy();

    // Click on "Lidas" tab
    fireEvent.press(screen.getByText("Lidas"));

    // Should still show read notifications (Lorem ipsum notifications are read)
    expect(screen.getAllByText(/Lorem ipsum/).length).toBeGreaterThan(0);

    // Click on "Não lidas" tab
    fireEvent.press(screen.getByText("Não lidas"));

    // Should show unread notifications
    expect(screen.getByText(/Phoenix Baker/)).toBeTruthy();
    expect(screen.getByText(/Club M Alphaville/)).toBeTruthy();
  });

  it("renders different icons for different notification types", () => {
    render(<Notifications />);

    // Should render general notifications icon for general notifications (without avatars)
    expect(
      screen.getAllByTestId("general-notifications-icon").length
    ).toBeGreaterThan(0);

    // Notifications with avatars should show images instead of icons
    // This is the expected behavior based on the implementation
  });

  it("handles action button presses", () => {
    const consoleSpy = jest.spyOn(console, "log").mockImplementation();

    render(<Notifications />);

    // Press the "Ver evento" button
    fireEvent.press(screen.getByText("Ver evento"));
    expect(consoleSpy).toHaveBeenCalledWith("Ver evento");

    // Press the "Ver enquete" button
    fireEvent.press(screen.getByText("Ver enquete"));
    expect(consoleSpy).toHaveBeenCalledWith("Ver enquete");

    consoleSpy.mockRestore();
  });
});
