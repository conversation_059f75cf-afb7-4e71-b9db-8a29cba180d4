import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828"
  },

  scrollContainer: {
    flex: 1
  },

  scrollContentContainer: {
    flexGrow: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 24
  },

  contentContainer: {
    alignItems: "center",
    width: "100%",
    maxWidth: 400
  },

  // Header styles
  headerIndicator: {
    width: 44,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginBottom: 32
  },

  headerTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 40
  },

  // Card image styles
  cardImageContainer: {
    alignItems: "center",
    marginBottom: 40
  },

  cardImage: {
    width: 240,
    height: 340,
    backgroundColor: "#1A1A1A",
    borderRadius: 16,
    justifyContent: "flex-end",
    alignItems: "flex-end",
    padding: 20,
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 8
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8
  },

  chipIcon: {
    width: 32,
    height: 24,
    backgroundColor: "#E5E5E5",
    borderRadius: 4,
    alignSelf: "flex-end"
  },

  // Section title styles
  sectionTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 32
  },

  // Form styles
  formContainer: {
    width: "100%",
    gap: 16,
    marginBottom: 32
  },

  // Input field styles
  inputFieldContainer: {
    width: "100%"
  },

  inputField: {
    borderWidth: 1,
    borderColor: "#4A5568",
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: "transparent",
    minHeight: 56
  },

  inputIcon: {
    width: 20,
    height: 20,
    marginRight: 12
  },

  inputText: {
    color: "#9CA3AF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    lineHeight: 20,
    flex: 1
  },

  // Two column layout for expiry and CVV
  twoColumnContainer: {
    flexDirection: "row",
    gap: 16,
    width: "100%"
  },

  halfWidthField: {
    flex: 1
  },

  // Button styles
  buttonContainer: {
    width: "100%",
    gap: 16,
    marginBottom: 32
  },

  primaryButton: {
    backgroundColor: "#10B981",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
    minHeight: 56
  },

  primaryButtonText: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24
  },

  secondaryButton: {
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
    minHeight: 56
  },

  secondaryButtonText: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
