import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image
} from "react-native";
import Screen<PERSON>ithHeader from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/main/search.style";
import SearchIcon from "@/components/icons/search-icon";
import CloseIcon from "@/components/icons/close-icon";
import MailIcon from "@/components/icons/mail-icon";
import AnnounceIcon from "@/components/icons/announce-icon";

interface SearchSuggestion {
  id: string;
  name: string;
  subtitle: string;
  type: "member" | "product" | "event";
  avatar?: string;
}

const SearchScreen: React.FC = () => {
  const {t} = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions] = useState<SearchSuggestion[]>([
    {
      id: "1",
      name: "<PERSON>",
      subtitle: "<EMAIL>",
      type: "member",
      avatar:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    },
    {
      id: "2",
      name: "Drew Cano",
      subtitle: "<EMAIL>",
      type: "member",
      avatar:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      id: "3",
      name: "E-book - Tendências de arquitetura em 2025",
      subtitle: "Produtos",
      type: "product"
    },
    {
      id: "4",
      name: "Mentoria Coletiva sobre Resoluções Jurídicas",
      subtitle: "Eventos",
      type: "event"
    },
    {
      id: "5",
      name: "Encontro anual de empreendedores em Balneário Camboriú - SC",
      subtitle: "Eventos",
      type: "event"
    }
  ]);

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleRemoveSuggestion = (suggestionId: string) => {
    // In a real app, this would remove the suggestion from the list
    console.log("Remove suggestion:", suggestionId);
  };

  return (
    <ScreenWithHeader screenTitle={t("search.title", "Busca")} backButton>
      <View style={styles.contentContainer}>
        {/* Search Input Row */}
        <View style={styles.searchRow}>
          <View style={styles.searchInputContainer}>
            <SearchIcon width={20} height={20} replaceColor="#F2F4F7" />
            <TextInput
              style={styles.searchInput}
              value={searchTerm}
              onChangeText={setSearchTerm}
              placeholder={t(
                "search.placeholder",
                "Buscar membros, eventos, etc..."
              )}
              placeholderTextColor="#F2F4F7"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>
          <TouchableOpacity
            onPress={handleClearSearch}
            style={styles.cancelButton}
          >
            <Text style={styles.cancelButtonText}>
              {t("search.cancel", "Cancelar")}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* Always show recent searches and categories */}
          <>
            {/* Recent Searches Header */}
            <View style={styles.recentSearchesHeader}>
              <Text style={styles.recentSearchesTitle}>
                {t("search.recentSearches", "Pesquisas recentes")}
              </Text>
              <TouchableOpacity style={styles.clearRecentButton}>
                <Text style={styles.clearRecentButtonText}>
                  {t("search.clearRecent", "Limpar recentes")}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Members Section */}
            <Text style={styles.categoryTitle}>
              {t("search.members", "Membros")}
            </Text>
            {suggestions
              .filter((s) => s.type === "member")
              .map((suggestion) => (
                <View key={suggestion.id} style={styles.suggestionItem}>
                  {suggestion.avatar ? (
                    <Image
                      source={{uri: suggestion.avatar}}
                      style={styles.suggestionAvatarImage}
                    />
                  ) : (
                    <View style={styles.suggestionAvatar}>
                      <Text style={styles.suggestionAvatarText}>
                        {getInitials(suggestion.name)}
                      </Text>
                    </View>
                  )}
                  <View style={styles.suggestionContent}>
                    <Text
                      style={styles.suggestionTitle}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {suggestion.name}
                    </Text>
                    <Text style={styles.suggestionSubtitle}>
                      {suggestion.subtitle}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveSuggestion(suggestion.id)}
                    style={styles.suggestionCloseButton}
                  >
                    <CloseIcon width={24} height={24} />
                  </TouchableOpacity>
                </View>
              ))}

            {/* Products Section */}
            <Text style={styles.categoryTitle}>
              {t("search.products", "Produtos")}
            </Text>
            {suggestions
              .filter((s) => s.type === "product")
              .map((suggestion) => (
                <View key={suggestion.id} style={styles.suggestionItem}>
                  <View style={styles.suggestionIconContainer}>
                    <MailIcon width={20} height={20} />
                  </View>
                  <View style={styles.suggestionContent}>
                    <Text
                      style={styles.suggestionTitle}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {suggestion.name}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveSuggestion(suggestion.id)}
                    style={styles.suggestionCloseButton}
                  >
                    <CloseIcon width={24} height={24} />
                  </TouchableOpacity>
                </View>
              ))}

            {/* Events Section */}
            <Text style={styles.categoryTitle}>
              {t("search.events", "Eventos")}
            </Text>
            {suggestions
              .filter((s) => s.type === "event")
              .map((suggestion) => (
                <View key={suggestion.id} style={styles.suggestionItem}>
                  <View style={styles.suggestionIconContainer}>
                    <AnnounceIcon
                      width={20}
                      height={20}
                      replaceColor="#EAECF0"
                    />
                  </View>
                  <View style={styles.suggestionContent}>
                    <Text
                      style={styles.suggestionTitle}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {suggestion.name}
                    </Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => handleRemoveSuggestion(suggestion.id)}
                    style={styles.suggestionCloseButton}
                  >
                    <CloseIcon width={24} height={24} />
                  </TouchableOpacity>
                </View>
              ))}
          </>
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default SearchScreen;
