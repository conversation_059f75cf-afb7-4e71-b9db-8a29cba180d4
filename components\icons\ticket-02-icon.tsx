import React from "react";
import Svg, {<PERSON>, SvgProps, Defs, <PERSON><PERSON><PERSON><PERSON>, <PERSON>} from "react-native-svg";

const Ticket02Icon: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Defs>
        <ClipPath id="clipPath4355832228">
          <Path 
            d="M0 0L20 0L20 20L0 20L0 0Z" 
            fillRule="nonzero" 
            transform="matrix(1 0 0 1 0 0)"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath4355832228)">
        <Path
          d="M4 14.1667Q2.7409 14.1667 2.29015 14.1298Q1.5009 14.0654 0.986698 13.8034Q0.0216796 13.3117 -0.470022 12.3466Q-0.732022 11.8324 -0.796505 11.0432Q-0.833333 10.5924 -0.833333 9.33333L-0.833333 4Q-0.833333 2.7409 -0.796505 2.29015Q-0.732022 1.5009 -0.470022 0.986698Q0.0216797 0.0216798 0.986698 -0.470022Q1.5009 -0.732022 2.29015 -0.796505Q2.7409 -0.833333 4 -0.833333L12.6667 -0.833333Q13.9258 -0.833333 14.3765 -0.796505Q15.1658 -0.732022 15.68 -0.470022Q16.645 0.0216811 17.1367 0.986699Q17.3987 1.5009 17.4632 2.29015Q17.5 2.74091 17.5 4L17.5 9.33333Q17.5 10.5924 17.4632 11.0432Q17.3987 11.8324 17.1367 12.3466Q16.645 13.3117 15.68 13.8034Q15.1658 14.0654 14.3765 14.1298Q13.9258 14.1667 12.6667 14.1667L4 14.1667ZM4 12.5L12.6667 12.5Q13.8578 12.5 14.2408 12.4687Q14.7022 12.431 14.9233 12.3183Q15.4058 12.0725 15.6517 11.59Q15.7643 11.3689 15.802 10.9075Q15.8333 10.5244 15.8333 9.33333L15.8333 4Q15.8333 2.80889 15.802 2.42586Q15.7643 1.96447 15.6517 1.74335Q15.4058 1.26084 14.9233 1.01499Q14.7022 0.902324 14.2408 0.864626Q13.8578 0.833333 12.6667 0.833333L4 0.833333Q2.80887 0.833333 2.42586 0.864626Q1.96447 0.902324 1.74335 1.01499Q1.26084 1.26084 1.01499 1.74335Q0.902324 1.96447 0.864626 2.42586Q0.833333 2.80887 0.833333 4L0.833333 9.33333Q0.833333 10.5245 0.864626 10.9075Q0.902324 11.3689 1.01499 11.59Q1.26084 12.0725 1.74335 12.3183Q1.96447 12.431 2.42586 12.4687Q2.80887 12.5 4 12.5ZM4.16667 2.5C4.16667 2.03977 4.53977 1.66667 5 1.66667C5.46023 1.66667 5.83333 2.03977 5.83333 2.5L5.83333 3.33333C5.83333 3.79357 5.46023 4.16667 5 4.16667C4.53977 4.16667 4.16667 3.79357 4.16667 3.33333L4.16667 2.5ZM4.16667 6.25C4.16667 5.78977 4.53977 5.41667 5 5.41667C5.46023 5.41667 5.83333 5.78977 5.83333 6.25L5.83333 7.08333C5.83333 7.54357 5.46023 7.91667 5 7.91667C4.53977 7.91667 4.16667 7.54357 4.16667 7.08333L4.16667 6.25ZM4.16667 10C4.16667 9.53977 4.53977 9.16667 5 9.16667C5.46023 9.16667 5.83333 9.53977 5.83333 10L5.83333 10.8333C5.83333 11.2936 5.46023 11.6667 5 11.6667C4.53977 11.6667 4.16667 11.2936 4.16667 10.8333L4.16667 10Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 1.66667 3.33333)"
          fill="rgb(251, 252, 252)"
        />
      </G>
    </Svg>
  );
};

export default Ticket02Icon;
