import React, {useCallback} from "react";
import {
  ScrollView,
  Text,
  View,
  TouchableOpacity,
  Linking,
  Alert,
  Image,
  StatusBar
} from "react-native";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import styles from "@/styles/settings/about-app.style";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import NewChevronRightIcon from "../../components/icons/new-chevron-right-icon";
import ScalesIcon from "../../components/icons/scales-icon";
import FacebookIcon from "../../components/icons/facebook-icon";
import XTwitterIcon from "../../components/icons/x-twitter-icon";
import InstagramIcon from "../../components/icons/instagram-icon";
import LinkedinIcon from "../../components/icons/linkedin-icon";
import YoutubeIcon from "../../components/icons/youtube-icon";

const AboutApp: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  const handleBackPress = useCallback(() => {
    router.back();
  }, [router]);

  const handleTermsPress = useCallback(() => {
    const termsUrl = "https://clubmbrasil.com.br/termos";
    Linking.openURL(termsUrl).catch(() => {
      Alert.alert(
        t("aboutApp.error", "Erro"),
        t("aboutApp.linkError", "Não foi possível abrir o link."),
        [{text: t("common.ok", "OK")}]
      );
    });
  }, [t]);

  const handleSocialMediaPress = useCallback(
    (platform: string) => {
      const urls = {
        youtube: "https://youtube.com/@clubmbrasil",
        facebook: "https://facebook.com/clubmbrasil",
        twitter: "https://twitter.com/clubmbrasil",
        instagram: "https://instagram.com/clubmbrasil",
        linkedin: "https://linkedin.com/company/clubmbrasil"
      };

      const url = urls[platform as keyof typeof urls];
      if (url) {
        Linking.openURL(url).catch(() => {
          Alert.alert(
            t("aboutApp.error", "Erro"),
            t("aboutApp.linkError", "Não foi possível abrir o link."),
            [{text: t("common.ok", "OK")}]
          );
        });
      }
    },
    [t]
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#111828" />

      {/* Status Bar */}
      <View
        style={{
          paddingHorizontal: 14.67,
          paddingTop: 18.07,
          paddingBottom: 9.83,
          flexDirection: "row",
          justifyContent: "space-between"
        }}
      >
        <Image
          source={{
            uri: "https://static.motiffcontent.com/private/resource/image/197ec08724c11f2-cb711796-a8e6-42a8-84a4-8eb59957e1ce.svg"
          }}
          style={{width: 28.43, height: 11.67, marginLeft: 18.78}}
          resizeMode="cover"
        />
        <Image
          source={{
            uri: "https://static.motiffcontent.com/private/resource/image/197ec08724ea943-08566eba-782e-4d25-a1a8-7493ee212a61.svg"
          }}
          style={{width: 66.66, height: 11.93, marginTop: 0.17}}
          resizeMode="cover"
        />
      </View>

      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={styles.scrollContainer}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <ChevronLeftIcon width={24} height={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Informações do aplicativo</Text>
        </View>

        {/* Logo */}
        <View style={styles.logoContainer}>
          <Image
            source={{
              uri: "https://static.motiffcontent.com/private/resource/image/197ec08724f3c29-43050ab8-c1d9-4586-9059-57f6493c14be.svg"
            }}
            style={styles.logo}
            resizeMode="cover"
          />
        </View>

        {/* Club M Logo PNG */}
        <View style={styles.clubMLogoContainer}>
          <Image
            source={require("../../assets/images/logo-about-app.png")}
            style={styles.clubMLogo}
            resizeMode="contain"
          />
        </View>

        {/* Terms and Conditions */}
        <TouchableOpacity
          onPress={handleTermsPress}
          style={styles.termsContainer}
        >
          <View style={styles.termsIcon}>
            <ScalesIcon width={21.86} height={21} />
          </View>
          <Text style={styles.termsText}>Termos e condições de uso</Text>
          <View style={styles.termsChevron}>
            <NewChevronRightIcon width={8} height={14} />
          </View>
        </TouchableOpacity>

        {/* Social Media Title */}
        <Text style={styles.socialMediaTitle}>
          Siga o Club M nas Redes Sociais.
        </Text>

        {/* Social Media Icons */}
        <View style={styles.socialMediaContainer}>
          <TouchableOpacity
            style={styles.socialMediaButton}
            onPress={() => handleSocialMediaPress("youtube")}
          >
            <YoutubeIcon width={19.29} height={19.29} replaceColor="#1D2838" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.socialMediaButton}
            onPress={() => handleSocialMediaPress("facebook")}
          >
            <FacebookIcon width={24} height={24} replaceColor="#DFE9F0" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.socialMediaButton}
            onPress={() => handleSocialMediaPress("twitter")}
          >
            <XTwitterIcon width={24} height={24} replaceColor="#000000" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.socialMediaButton}
            onPress={() => handleSocialMediaPress("instagram")}
          >
            <InstagramIcon width={24} height={24} replaceColor="#DFE9F0" />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.socialMediaButton}
            onPress={() => handleSocialMediaPress("linkedin")}
          >
            <LinkedinIcon width={24} height={24} replaceColor="#1275B1" />
          </TouchableOpacity>
        </View>

        {/* Version */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>VERSÃO 1.0.34</Text>
        </View>

        {/* Home Indicator */}
        <View style={styles.homeIndicator}>
          <Image
            source={{
              uri: "https://static.motiffcontent.com/private/resource/image/197eb70cc139df0-ee8bc1d3-0978-4c82-a7c3-7b50c80d18fd.svg"
            }}
            style={styles.homeIndicatorBar}
            resizeMode="cover"
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default AboutApp;
