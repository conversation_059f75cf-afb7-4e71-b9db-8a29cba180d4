import React, {useMemo} from "react";
import Svg, {Path, SvgProps, Defs, ClipPath, G} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ShoppingBagIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const ShoppingBagIcon: React.FC<ShoppingBagIconProps> = (props) => {
  const color = useMemo(() => props.replaceColor ?? "#EAECF0", [props.replaceColor]);

  return (
    <Svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Defs>
        <ClipPath id="clipPath0134790906">
          <Path 
            d="M0 0L20 0L20 20L0 20L0 0Z" 
            fillRule="nonzero" 
            transform="matrix(1 0 0 1 0 0)"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath0134790906)">
        <Path
          d="M-0.133336 2.12222L1.43333 0.0333328Q1.72333 -0.353331 1.90921 -0.494374Q2.15831 -0.683388 2.45898 -0.769257Q2.68334 -0.833333 3.16667 -0.833333L11.8333 -0.833333Q12.3167 -0.833333 12.541 -0.769257Q12.8417 -0.683388 13.0908 -0.494374Q13.2767 -0.353332 13.5667 0.0333254L15.1333 2.12222Q15.4786 2.58254 15.584 2.80046Q15.7248 3.09172 15.7869 3.40925Q15.8333 3.64681 15.8333 4.22219L15.8333 14Q15.8333 14.8507 15.8079 15.1625Q15.7604 15.7433 15.5608 16.135Q15.1921 16.8587 14.4683 17.2275Q14.0767 17.4271 13.4959 17.4745Q13.1841 17.5 12.3333 17.5L2.66667 17.5Q1.81594 17.5 1.50414 17.4745Q0.923342 17.4271 0.53169 17.2275Q-0.192073 16.8587 -0.560849 16.135Q-0.760405 15.7433 -0.807859 15.1625Q-0.833333 14.8507 -0.833333 14L-0.833333 4.22222Q-0.833333 3.64682 -0.786892 3.40925Q-0.72482 3.09173 -0.583961 2.80046Q-0.478573 2.58254 -0.133336 2.12222ZM13.3333 2.5L1.66667 2.5L2.76667 1.03333Q2.76846 1.03094 2.77024 1.02857Q2.88231 0.879292 2.922 0.836157Q2.97497 0.833333 3.16667 0.833333L11.8333 0.833333Q12.025 0.833333 12.078 0.836157L12.0805 0.834825Q12.0823 0.836936 12.0843 0.839293Q12.1183 0.879978 12.2333 1.03333L13.3333 2.5ZM0.833436 4.15731Q1.00149 4.16667 1.33333 4.16667L13.6667 4.16667Q13.9985 4.16667 14.1666 4.15731Q14.1667 4.18879 14.1667 4.22219L14.1667 14Q14.1667 14.7828 14.1467 15.0268Q14.1261 15.2798 14.0758 15.3783Q13.9529 15.6196 13.7117 15.7425Q13.6131 15.7927 13.3601 15.8134Q13.1161 15.8333 12.3333 15.8333L2.66667 15.8333Q1.88391 15.8333 1.63986 15.8134Q1.3869 15.7927 1.28834 15.7425Q1.04709 15.6196 0.924161 15.3783Q0.87394 15.2798 0.853273 15.0268Q0.833333 14.7828 0.833333 14L0.833333 4.22222Q0.833333 4.18866 0.833436 4.15731ZM3.33333 6.66667C3.33333 6.20643 3.70643 5.83333 4.16667 5.83333C4.6269 5.83333 5 6.20643 5 6.66667Q5 7.16395 5.1903 7.62337Q5.3806 8.0828 5.73223 8.43443Q6.08386 8.78606 6.54329 8.97636Q7.00272 9.16667 7.5 9.16667Q7.99728 9.16667 8.45671 8.97636Q8.91614 8.78606 9.26777 8.43443Q9.6194 8.0828 9.8097 7.62337Q10 7.16395 10 6.66667C10 6.20643 10.3731 5.83333 10.8333 5.83333C11.2936 5.83333 11.6667 6.20643 11.6667 6.66667Q11.6667 7.49547 11.3495 8.26118Q11.0323 9.02689 10.4463 9.61294Q9.86023 10.199 9.09451 10.5162Q8.3288 10.8333 7.5 10.8333Q6.6712 10.8333 5.90548 10.5162Q5.13977 10.199 4.55372 9.61294Q3.96767 9.02689 3.6505 8.26118Q3.33333 7.49547 3.33333 6.66667Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 2.5 1.66667)"
          fill={color}
        />
      </G>
    </Svg>
  );
};

export default ShoppingBagIcon;
