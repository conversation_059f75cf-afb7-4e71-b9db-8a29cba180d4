import React, {useState, useRef} from "react";
import {Text, View, TouchableOpacity, ScrollView} from "react-native";
import {Image} from "expo-image";
import <PERSON>WithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import PlusIcon from "../../components/icons/plus-icon";
import EyeIcon from "../../components/icons/eye-icon";
import DisableEyeIcon from "../../components/icons/disable-eye-icon";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";
import CreditCard from "../../components/credit-card";
import styles from "@/styles/wallet/wallet.style";

interface MemberCard {
  id: string;
  name: string;
  photo: string;
  membershipType: string;
  cardNumber: string;
  federationUnit: string;
  associationUnit: string;
  activeSince: string;
  isDefault: boolean;
  cardType: "member" | "american-express" | "visa";
  expiryDate?: string;
}

const Wallet: React.FC = () => {
  const {t} = useTranslation();
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [showCardInfo, setShowCardInfo] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const scrollViewRef = useRef<ScrollView>(null);
  const cardWidth = 212; // Width of card + margin

  const [memberCards] = useState<MemberCard[]>([
    {
      id: "1",
      name: "Maria Aparecida dos Santos",
      photo:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro VIP",
      cardNumber: "1234",
      federationUnit: "Balneário Camboriú - SC",
      associationUnit: "Unidade Osvaldo Reis",
      activeSince: "Setembro de 2025",
      isDefault: true,
      cardType: "member"
    },
    {
      id: "2",
      name: "João Silva Santos",
      photo:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro Premium",
      cardNumber: "**** **** **** 5678",
      federationUnit: "São Paulo - SP",
      associationUnit: "Unidade Central",
      activeSince: "Janeiro de 2024",
      isDefault: false,
      cardType: "american-express",
      expiryDate: "12/28"
    },
    {
      id: "3",
      name: "Ana Costa Lima",
      photo:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro VIP",
      cardNumber: "**** **** **** 9012",
      federationUnit: "Rio de Janeiro - RJ",
      associationUnit: "Unidade Copacabana",
      activeSince: "Março de 2023",
      isDefault: false,
      cardType: "visa",
      expiryDate: "08/27"
    },
    {
      id: "4",
      name: "Carlos Mendes",
      photo:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      membershipType: "Membro Standard",
      cardNumber: "**** **** **** 3456",
      federationUnit: "Belo Horizonte - MG",
      associationUnit: "Unidade Savassi",
      activeSince: "Julho de 2022",
      isDefault: false,
      cardType: "american-express",
      expiryDate: "05/26"
    }
  ]);

  const currentCard = memberCards[currentCardIndex];

  const handleScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const index = Math.round(contentOffsetX / cardWidth);

    // Ensure index is within bounds
    if (index >= 0 && index < memberCards.length) {
      setCurrentCardIndex(index);
    }
  };

  const handlePrevCard = () => {
    const newIndex =
      currentCardIndex === 0 ? memberCards.length - 1 : currentCardIndex - 1;
    setCurrentCardIndex(newIndex);

    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: newIndex * cardWidth,
        animated: true
      });
    }
  };

  const handleNextCard = () => {
    const newIndex =
      currentCardIndex === memberCards.length - 1 ? 0 : currentCardIndex + 1;
    setCurrentCardIndex(newIndex);

    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: newIndex * cardWidth,
        animated: true
      });
    }
  };

  return (
    <ScreenWithHeader
      screenTitle={t("wallet.title", "Sua carteira")}
      backButton
      rightHeaderChild={
        <TouchableOpacity style={styles.addCardButton}>
          <Text style={styles.addCardButtonText}>
            {t("wallet.addCard", "Ad. cartão")}
          </Text>
          <PlusIcon width={20} height={20} />
        </TouchableOpacity>
      }
    >
      <View style={styles.container}>
        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 0 && styles.tabButtonActive
            ]}
            onPress={() => setActiveTab(0)}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 0 && styles.tabButtonTextActive
              ]}
            >
              {t("wallet.memberCard", "Cartão de associado")}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              activeTab === 1 && styles.tabButtonActive
            ]}
            onPress={() => setActiveTab(1)}
          >
            <Text
              style={[
                styles.tabButtonText,
                activeTab === 1 && styles.tabButtonTextActive
              ]}
            >
              {t("wallet.yourCards", "Seus cartões")}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Card Carousel */}
        <View style={styles.carouselContainer}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollViewContent}
            style={styles.scrollView}
            snapToInterval={cardWidth}
            decelerationRate="fast"
            snapToAlignment="center"
            onScroll={handleScroll}
            scrollEventThrottle={16}
            contentOffset={{
              x: currentCardIndex * cardWidth,
              y: 0
            }}
          >
            {memberCards.map((card, index) => (
              <TouchableOpacity
                key={`${card.id}-${index}`}
                style={[
                  styles.cardContainer,
                  index === 0 && styles.firstCard,
                  index === memberCards.length - 1 && styles.lastCard
                ]}
                onPress={() => {
                  setCurrentCardIndex(index);
                  scrollViewRef.current?.scrollTo({
                    x: index * cardWidth,
                    animated: true
                  });
                }}
              >
                {card.cardType === "member" ? (
                  <View style={styles.memberCard}>
                    <View style={styles.cardLeftSection}>
                      <Image
                        source={{
                          uri: "https://static.motiffcontent.com/private/resource/image/197eab58784d742-cabc2745-ca54-47d1-b36c-2011ce6c4665.svg"
                        }}
                        style={styles.cardLogo}
                        contentFit="cover"
                      />
                    </View>
                    <View style={styles.cardRightSection}>
                      <Image
                        source={{
                          uri: "https://static.motiffcontent.com/private/resource/image/197eab587855421-f1b5f03a-692d-4194-af66-6c4adb1063c9.svg"
                        }}
                        style={styles.cardBackground}
                        contentFit="cover"
                      />
                      <View style={styles.memberInfoOverlay}>
                        <View style={{width: 172, minHeight: 40}}>
                          <Text style={styles.memberName}>{card.name}</Text>
                        </View>
                        <View style={{width: 172, minHeight: 18}}>
                          <Text style={styles.membershipType}>
                            {card.membershipType}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                ) : (
                  <CreditCard
                    cardType={card.cardType}
                    cardNumber={card.cardNumber}
                    holderName={card.name}
                    expiryDate={card.expiryDate ?? ""}
                  />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Navigation */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              style={styles.navigationButton}
              onPress={handlePrevCard}
            >
              <ChevronLeftIcon width={24} height={24} />
            </TouchableOpacity>

            <View style={styles.paginationWrapper}>
              {memberCards.map((card, index) => (
                <View
                  key={`dot-${card.id}`}
                  style={[
                    styles.paginationDot,
                    index === currentCardIndex && styles.paginationIndicator
                  ]}
                />
              ))}
            </View>

            <TouchableOpacity
              style={styles.navigationButton}
              onPress={handleNextCard}
            >
              <ChevronRightIcon width={24} height={24} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Card Information Section */}
        <View style={styles.cardInfoContainer}>
          <View style={styles.cardInfoHeader}>
            <Text style={styles.cardInfoTitle}>
              {t("wallet.cardInfo", "Informações do cartão")}
            </Text>
            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => setShowCardInfo(!showCardInfo)}
            >
              {showCardInfo ? (
                <DisableEyeIcon width={20} height={20} />
              ) : (
                <EyeIcon width={20} height={20} />
              )}
              <Text style={styles.toggleButtonText}>
                {showCardInfo
                  ? t("wallet.hide", "Esconder")
                  : t("wallet.show", "Mostrar")}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.cardInfoContent}>
            <View style={styles.infoRowAlternate}>
              <Text style={[styles.infoLabel, styles.infoLabelName]}>
                {t("wallet.memberName", "Nome do associado")}
              </Text>
              <Text style={[styles.infoValue, styles.infoValueName]}>
                {currentCard.name}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, styles.infoLabelFederation]}>
                {t("wallet.federationUnit", "Unidade Federativa")}
              </Text>
              <Text style={[styles.infoValue, styles.infoValueFederation]}>
                {currentCard.federationUnit}
              </Text>
            </View>

            <View style={styles.infoRowAlternate}>
              <Text style={[styles.infoLabel, styles.infoLabelAssociation]}>
                {t("wallet.associationUnit", "Unidade de Associação")}
              </Text>
              <Text style={[styles.infoValue, styles.infoValueAssociation]}>
                {currentCard.associationUnit}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={[styles.infoLabel, styles.infoLabelActive]}>
                {t("wallet.activeSince", "Membro ativo desde")}
              </Text>
              <Text style={[styles.infoValue, styles.infoValueActive]}>
                {currentCard.activeSince}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default Wallet;
