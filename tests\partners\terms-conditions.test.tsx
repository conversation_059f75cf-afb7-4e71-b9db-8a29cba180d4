import React from "react";
import {render, fireEvent} from "@testing-library/react-native";
import TermsConditionsScreen from "../../app/(main)/partners/terms-conditions";

// Mock the router
const mockBack = jest.fn();
jest.mock("expo-router", () => ({
  useRouter: () => ({
    back: mockBack
  })
}));

// Mock the translation hook
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback || key
  })
}));

// Mock the ScreenWithHeader component
jest.mock("../../components/screen-with-header", () => {
  const {View} = require("react-native");
  return ({children, screenTitle}: any) => (
    <View testID="screen-with-header" accessibilityLabel={screenTitle}>
      {children}
    </View>
  );
});

// Mock the FullSizeButton component
jest.mock("../../components/full-size-button", () => {
  const {TouchableOpacity, Text} = require("react-native");
  return ({text, onPress, variant}: any) => (
    <TouchableOpacity testID="full-size-button" onPress={onPress}>
      <Text>{text}</Text>
    </TouchableOpacity>
  );
});

// Mock the styles
jest.mock("@/styles/partners/terms-conditions.style", () => ({
  container: {},
  contentContainer: {},
  contentHeader: {},
  contentTitle: {},
  scrollContainer: {},
  description: {},
  disclaimerContainer: {},
  disclaimerText: {},
  disclaimerTextNormal: {},
  disclaimerTextBold: {},
  buttonContainer: {}
}));

describe("TermsConditionsScreen", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly with all required elements", () => {
    const {getByTestId, getByText} = render(<TermsConditionsScreen />);

    // Check if the screen wrapper is rendered
    expect(getByTestId("screen-with-header")).toBeTruthy();

    // Check if the content title is rendered
    expect(getByText("Lorem ipsum dolor sit amet")).toBeTruthy();

    // Check if the disclaimer text is rendered
    expect(getByText("Ao adquirir o benefício você aceita os ")).toBeTruthy();
    expect(getByText("termos e condições de uso")).toBeTruthy();

    // Check if the close button is rendered
    expect(getByTestId("full-size-button")).toBeTruthy();
    expect(getByText("Fechar")).toBeTruthy();
  });

  it("calls router.back when close button is pressed", () => {
    const {getByTestId} = render(<TermsConditionsScreen />);

    const closeButton = getByTestId("full-size-button");
    fireEvent.press(closeButton);

    expect(mockBack).toHaveBeenCalledTimes(1);
  });

  it("renders the content with proper structure", () => {
    const {getByText} = render(<TermsConditionsScreen />);

    // Check if the main content text is rendered
    expect(getByText(/Dolor enim eu tortor urna sed duis nulla/)).toBeTruthy();

    // Check if the disclaimer parts are rendered separately
    expect(getByText("Ao adquirir o benefício você aceita os ")).toBeTruthy();
    expect(getByText("termos e condições de uso")).toBeTruthy();
    expect(getByText(".")).toBeTruthy();
  });
});
