import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import FullSizeButton from "../../../components/full-size-button";
import styles from "@/styles/partners/terms-conditions.style";

const TermsConditionsScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();

  const handleClose = () => {
    router.back();
  };

  return (
    <ScreenWithHeader
      screenTitle={t(
        "partners.termsConditions",
        "Termos e condições do parceiro"
      )}
      backButton
    >
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.contentHeader}>
            <Text style={styles.contentTitle}>
              {t("partners.contentTitle", "Lorem ipsum dolor sit amet")}
            </Text>
          </View>

          <ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
          >
            <Text style={styles.description}>
              {t(
                "partners.termsContent",
                "Dolor enim eu tortor urna sed duis nulla. Aliquam vestibulum, nulla odio nisl vitae. In aliquet pellentesque aenean hac vestibulum turpis mi bibendum diam. Tempor integer aliquam in vitae malesuada fringilla.\n\nElit nisi in eleifend sed nisi. Pulvinar at orci, proin imperdiet commodo consectetur convallis risus. Sed condimentum enim dignissim adipiscing faucibus consequat, urna. Viverra purus et erat auctor aliquam. Risus, volutpat vulputate posuere purus sit congue convallis aliquet. Arcu id augue ut feugiat donec porttitor neque. Mauris, neque ultricies eu vestibulum, bibendum quam lorem id. Dolor lacus, eget nunc lectus in tellus, pharetra, porttitor.\n\nIpsum sit mattis nulla quam nulla. Gravida id gravida ac enim mauris id. Non pellentesque congue eget consectetur turpis. Sapien, dictum molestie sem tempor. Diam elit, orci, tincidunt aenean tempus. Quis velit eget ut tortor tellus. Sed vel, congue felis elit erat nam nibh orci."
              )}
            </Text>
          </ScrollView>
        </View>

        <View style={styles.disclaimerContainer}>
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerTextNormal}>
              {t(
                "partners.disclaimerStart",
                "Ao adquirir o benefício você aceita os "
              )}
            </Text>
            <Text style={styles.disclaimerTextBold}>
              {t("partners.disclaimerBold", "termos e condições de uso")}
            </Text>
            <Text style={styles.disclaimerTextNormal}>
              {t("partners.disclaimerEnd", ".")}
            </Text>
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <FullSizeButton
            text={t("partners.close", "Fechar")}
            onPress={handleClose}
            variant="secondary"
          />
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default TermsConditionsScreen;
