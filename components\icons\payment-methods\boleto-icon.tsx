import React from "react";
import Svg, {SvgProps, Path, Rect} from "react-native-svg";

const BoletoIcon: React.FC<SvgProps> = (props) => {
    const width = props.width ?? 34;
    const height = props.height ?? 24;

    return (
        <Svg
            width={width}
            height={height}
            viewBox="0 0 34 24"
            fill="none"
            {...props}
        >
            {/* Background */}
            <Rect
                x="0"
                y="0"
                width="34"
                height="24"
                rx="2"
                fill="#F2F3F6"
            />
            {/* Barcode lines */}
            <Rect x="3" y="6" width="1" height="12" fill="#000000" />
            <Rect x="5" y="6" width="2" height="12" fill="#000000" />
            <Rect x="8" y="6" width="1" height="12" fill="#000000" />
            <Rect x="10" y="6" width="1" height="12" fill="#000000" />
            <Rect x="12" y="6" width="2" height="12" fill="#000000" />
            <Rect x="15" y="6" width="1" height="12" fill="#000000" />
            <Rect x="17" y="6" width="1" height="12" fill="#000000" />
            <Rect x="19" y="6" width="2" height="12" fill="#000000" />
            <Rect x="22" y="6" width="1" height="12" fill="#000000" />
            <Rect x="24" y="6" width="1" height="12" fill="#000000" />
            <Rect x="26" y="6" width="2" height="12" fill="#000000" />
            <Rect x="29" y="6" width="1" height="12" fill="#000000" />
            <Rect x="31" y="6" width="1" height="12" fill="#000000" />
        </Svg>
    );
};

export default BoletoIcon;
