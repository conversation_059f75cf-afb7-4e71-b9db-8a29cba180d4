import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Rect = (props: any) => <rect {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Rect
  };
});

import TicketBackgroundSvg from "../../../components/icons/ticket-background-svg";

describe("TicketBackgroundSvg", () => {
  it("renders without crash", () => {
    const { toJSON } = render(<TicketBackgroundSvg />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("uses default dimensions when none provided", () => {
    const { getByTestId } = render(<TicketBackgroundSvg testID="svg" />);
    const svg = getByTestId("svg");
    expect(svg.props.width).toBe(327);
    expect(svg.props.height).toBe(522);
  });

  it("allows custom dimensions", () => {
    const { getByTestId } = render(
      <TicketBackgroundSvg width={400} height={600} testID="svg" />
    );
    const svg = getByTestId("svg");
    expect(svg.props.width).toBe(400);
    expect(svg.props.height).toBe(600);
  });

  it("maintains correct viewBox", () => {
    const { getByTestId } = render(<TicketBackgroundSvg testID="svg" />);
    const svg = getByTestId("svg");
    expect(svg.props.viewBox).toBe("0 0 327 522");
  });
});
