import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface YoutubeIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const YoutubeIcon: React.FC<YoutubeIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#DFE9F0",
    [props.replaceColor]
  );

  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-1.94 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M9.75 15.02l5.75-3.27-5.75-3.27v6.54z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default YoutubeIcon;
