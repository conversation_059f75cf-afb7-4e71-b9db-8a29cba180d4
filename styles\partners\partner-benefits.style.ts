import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#111828"
  },
  scrollView: {
    flex: 1
  },
  headerBackground: {
    overflow: "hidden"
  },
  headerBackgroundImage: {
    opacity: 0.08,
    transform: [
      {
        scale: 1.8
      }
    ]
  },
  headerGradient: {
    paddingTop: 32,
    paddingBottom: 40,
    minHeight: 220
  },
  header: {
    alignItems: "center",
    paddingHorizontal: 24
  },
  logoContainer: {
    width: 120,
    height: 120,
    backgroundColor: "#1F2238",
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    borderWidth: 1,
    borderColor: "#2D3748",
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8
  },
  logo: {
    width: "100%",
    height: "100%"
  },
  logoTriangle: {
    width: 28,
    height: 28,
    backgroundColor: "#8B5CF6", // Slightly lighter purple for better visibility
    borderRadius: 6,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12
  },
  logoTriangleSymbol: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 10,
    fontWeight: "700"
  },
  logoText: {
    color: "#8B5CF6", // Match the triangle color
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 22,
    textAlign: "center"
  },
  benefitInfo: {
    paddingHorizontal: 24,
    marginBottom: 16
  },
  benefitTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "700",
    lineHeight: 28,
    marginBottom: 8,
    textAlign: "center"
  },
  benefitDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  seeMoreLink: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600"
  },
  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  tabContainer: {
    flexDirection: "row",
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085",
    gap: 12,
    marginTop: 4
  },
  tab: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "center"
  },
  badgeContainer: {
    alignItems: "center",
    marginBottom: 16
  },
  limitedTimeBadge: {
    backgroundColor: stylesConstants.colors.fullWhite,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12
  },
  badgeText: {
    color: stylesConstants.colors.error600,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "700",
    lineHeight: 14,
    textAlign: "center"
  },
  acquireSection: {
    paddingHorizontal: 24,
    paddingTop: 20,
    gap: 16
  },
  validUntil: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    marginTop: 12
  },
  detailsSection: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    marginHorizontal: 24,
    marginBottom: 32,
    paddingTop: 16,
    paddingBottom: 16
  },

  // Tabs styles
  tabsContainer: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#EAECF0",
    paddingHorizontal: 24
  },
  activeTab: {
    flexGrow: 1,
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 1,
    minHeight: 33,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: stylesConstants.colors.fullWhite
  },
  inactiveTab: {
    flexGrow: 1,
    paddingLeft: 4,
    paddingRight: 4,
    paddingTop: 1,
    minHeight: 33,
    justifyContent: "center",
    alignItems: "flex-start"
  },
  activeTabText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    flexGrow: 1,
    minHeight: 20,
    textAlign: "center"
  },
  inactiveTabText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    flexGrow: 1,
    minHeight: 20,
    textAlign: "center"
  },

  // Content styles
  detailsContent: {
    gap: 0,
    marginTop: 4
  },
  detailItemWithBackground: {
    backgroundColor: "#344054",
    borderRadius: 4,
    padding: 8,
    flexDirection: "row",
    gap: 8,
    alignItems: "flex-start",
    marginHorizontal: 16,
    marginBottom: 12,
    overflow: "hidden"
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    minHeight: 18,
    marginBottom: 12
  },
  detailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    minHeight: 18,
    flex: 1
  },
  detailValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    textAlign: "right",
    minHeight: 18,
    flexShrink: 0
  },
  detailValueMultiline: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    textAlign: "right",
    flex: 1,
    flexShrink: 1
  },
  detailLink: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18,
    minHeight: 18,
    flexShrink: 0
  },
  detailLabelContainer: {
    flex: 1,
    maxWidth: "40%"
  },
  detailValueContainer: {
    flex: 1,
    maxWidth: "60%"
  },

  // Usage tab styles
  usageLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    minHeight: 18,
    flexGrow: 1,
    flexBasis: 135.5
  },
  usageDiscount: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    textAlign: "right",
    minHeight: 18,
    width: 50,
    flexShrink: 0
  },

  // Keep section title for other sections
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 16
  },
  termsLink: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    marginBottom: 20
  },
  validityInfo: {
    marginTop: 4
  },
  validUntilLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 4
  },
  validUntilValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  paymentSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  paymentMethods: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
    marginTop: 4
  },
  paymentMethodIcon: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 4,
    padding: 6,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 1
  },
  paymentNote: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  socialSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  socialItem: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  socialIconContainer: {
    width: 40,
    height: 40,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12
  },
  socialContent: {
    flex: 1
  },
  socialText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    marginBottom: 2
  },
  socialUrl: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },
  socialArrow: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  arrowIcon: {
    color: stylesConstants.colors.textPrimary,
    fontSize: 16,
    fontWeight: "600"
  },
  buyNowSection: {
    paddingHorizontal: 24,
    paddingBottom: 32,
    paddingTop: 8
  }
});

export default styles;
