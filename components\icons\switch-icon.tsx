import React from "react";
import Svg, {<PERSON>, SvgProps, Defs, <PERSON>lip<PERSON><PERSON>, G} from "react-native-svg";

const SwitchIcon: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Defs>
        <ClipPath id="clipPath9578280473">
          <Path 
            d="M0 0L20 0L20 20L0 20L0 0Z" 
            fillRule="nonzero" 
            transform="matrix(1 0 0 1 0 0)"
          />
        </ClipPath>
      </Defs>
      <G clipPath="url(#clipPath9578280473)">
        <Path
          d="M3.92259 -0.589256C3.59715 -0.91469 3.06951 -0.91469 2.74408 -0.589256L-0.589256 2.74408Q-0.706466 2.86129 -0.7699 3.01443Q-0.833333 3.16757 -0.833333 3.33333Q-0.833333 3.49909 -0.7699 3.65224Q-0.706466 3.80538 -0.589256 3.92259L2.74408 7.25592C3.06951 7.58136 3.59715 7.58136 3.92259 7.25592C4.24802 6.93049 4.24802 6.40284 3.92259 6.07741L2.01184 4.16667L13.3333 4.16667C13.7936 4.16667 14.1667 3.79357 14.1667 3.33333C14.1667 2.8731 13.7936 2.5 13.3333 2.5L2.01184 2.5L3.92259 0.589256C4.24802 0.263821 4.24802 -0.263822 3.92259 -0.589256ZM9.41074 7.74408C9.08531 8.06951 9.08531 8.59715 9.41074 8.92259L11.3215 10.8333L0 10.8333C-0.460233 10.8333 -0.833333 11.2064 -0.833333 11.6667C-0.833333 12.1269 -0.460233 12.5 0 12.5L11.3215 12.5L9.41074 14.4107C9.08531 14.7362 9.08531 15.2638 9.41074 15.5893C9.73618 15.9147 10.2638 15.9147 10.5893 15.5893L13.9226 12.2559Q14.0398 12.1387 14.1032 11.9856Q14.1667 11.8324 14.1667 11.6667Q14.1667 11.5009 14.1032 11.3478Q14.0398 11.1946 13.9226 11.0774L10.5893 7.74408C10.2638 7.41864 9.73618 7.41864 9.41074 7.74408Z"
          fillRule="evenodd"
          transform="matrix(1 0 0 1 3.33333 2.5)"
          fill="#FFFFFF"
        />
      </G>
    </Svg>
  );
};

export default SwitchIcon;
