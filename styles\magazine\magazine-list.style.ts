import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  // Search Section
  searchContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    paddingTop: 24 // Add top padding since we disabled Screen<PERSON>ithHeader padding
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12
  },
  searchInput: {
    flex: 1,
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  },

  // Carousel Section
  carouselContainer: {
    marginBottom: 32,
    position: "relative"
  },
  carousel: {
    flex: 1
  },
  carouselItemWrapper: {
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 24
  },
  carouselItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 16,
    overflow: "hidden",
    position: "relative",
    flex: 1
  },
  carouselImagePlaceholder: {
    width: "100%",
    height: 200,
    backgroundColor: stylesConstants.colors.brand.primary,
    justifyContent: "center",
    alignItems: "center"
  },
  carouselImage: {
    width: "100%",
    height: 200,
    position: "absolute",
    top: 0,
    left: 0
  },
  carouselBadge: {
    position: "absolute",
    top: 16,
    left: 16,
    backgroundColor: stylesConstants.colors.brand.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    zIndex: 1
  },
  carouselBadgeText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 16
  },
  carouselTextContent: {
    padding: 20
  },
  carouselTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 8
  },
  carouselDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },

  // Carousel Navigation
  carouselArrow: {
    position: "absolute",
    top: "50%",
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10
  },
  carouselArrowLeft: {
    left: 8,
    marginTop: -20
  },
  carouselArrowRight: {
    right: 8,
    marginTop: -20
  },
  carouselArrowText: {
    color: stylesConstants.colors.fullWhite,
    fontSize: 24,
    fontWeight: "bold"
  },

  // Pagination Dots
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 16,
    gap: 8
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.3)"
  },
  paginationDotActive: {
    backgroundColor: stylesConstants.colors.fullWhite,
    width: 24
  },

  // Recent Editions Section
  recentContainer: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 16
  },
  recentGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 16,
    justifyContent: "space-between"
  },
  recentCard: {
    width: "48%",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    overflow: "hidden"
  },
  recentImage: {
    width: "100%",
    height: 120
  },
  recentContent: {
    padding: 12
  },
  recentCategory: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 16,
    marginBottom: 4
  },
  recentDate: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 14,
    opacity: 0.7
  },

  // Category Sections
  categoryContainer: {
    marginBottom: 32
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 24,
    marginBottom: 16
  },
  categoryTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 24
  },
  seeAllText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },

  // Horizontal Scroll Carousels
  horizontalScroll: {
    paddingLeft: 24
  },
  horizontalScrollContent: {
    paddingRight: 24,
    gap: 16
  },
  horizontalCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    overflow: "hidden",
    width: 140
  },
  horizontalImageContainer: {
    width: "100%",
    height: 180,
    backgroundColor: "#333"
  },
  horizontalImage: {
    width: "100%",
    height: "100%"
  },
  horizontalContent: {
    padding: 12
  },
  horizontalTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 16,
    marginBottom: 4
  },
  horizontalDate: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 14
  },

  // Back to Top Button
  backToTopButton: {
    alignSelf: "center",
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 32
  },
  backToTopText: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
