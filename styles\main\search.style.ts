import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  searchRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    marginBottom: 24
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 24,
    fontWeight: 700,
    lineHeight: 32,
    marginBottom: 8
  },
  searchContainer: {
    marginBottom: 20
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C2230",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: "#F2F4F7",
    flex: 1,
    gap: 8,
    height: 40
  },
  searchIcon: {
    marginRight: 12
  },
  searchInput: {
    flex: 1,
    color: "#F2F4F7",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    height: 20
  },
  cancelButton: {
    marginTop: 10
  },
  cancelButtonText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    width: 57,
    minHeight: 20
  },
  filtersContainer: {
    marginBottom: 24
  },
  filterSection: {
    marginBottom: 16
  },
  filterTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    marginBottom: 8
  },
  filterList: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8
  },
  filterChip: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  filterChipActive: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary
  },
  filterChipText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18
  },
  filterChipTextActive: {
    color: stylesConstants.colors.fullWhite,
    fontWeight: 600
  },
  resultsContainer: {
    flex: 1
  },
  resultsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  resultsCount: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  sortButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  sortButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  resultsList: {
    gap: 16
  },
  resultItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault
  },
  resultHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8
  },
  resultTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    flex: 1,
    marginRight: 12
  },
  resultType: {
    backgroundColor: stylesConstants.colors.brand.brand25,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4
  },
  resultTypeText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 600,
    lineHeight: 14
  },
  resultDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    marginBottom: 8
  },
  resultMeta: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  resultMetaText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18
  },
  suggestionItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    paddingHorizontal: 24,
    paddingVertical: 6,
    marginBottom: 2
  },
  suggestionAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: stylesConstants.colors.gray700,
    justifyContent: "center",
    alignItems: "center"
  },
  suggestionAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  suggestionAvatarText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    textAlign: "center"
  },
  suggestionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#475467",
    justifyContent: "center",
    alignItems: "center"
  },
  suggestionContent: {
    flex: 1,
    gap: 2
  },
  suggestionTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 20,
    textOverflow: "ellipsis"
  },
  suggestionSubtitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 18
  },
  suggestionCloseButton: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  suggestionCloseButtonText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 400
  },
  recentSearchesHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
    paddingHorizontal: 24
  },
  recentSearchesTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    width: 107,
    textOverflow: "ellipsis",
    minHeight: 18
  },
  clearRecentButton: {
    width: 95,
    alignItems: "flex-start",
    minHeight: 18
  },
  clearRecentButtonText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18,
    width: 95,
    minHeight: 18
  },
  categoryTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 18,
    marginBottom: 6,
    marginTop: 12,
    paddingHorizontal: 24,
    minHeight: 18
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40
  },
  emptyStateTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: 600,
    lineHeight: 26,
    textAlign: "center",
    marginBottom: 8
  },
  emptyStateText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    textAlign: "center"
  }
});

export default styles;
