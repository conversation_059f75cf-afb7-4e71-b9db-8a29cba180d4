import React from "react";
import {Text, View, TouchableOpacity, StatusBar} from "react-native";
import {router, useLocalSearchParams} from "expo-router";
import styles from "@/styles/events/payment-review.style";
import ChevronLeftIcon from "@/components/icons/chevron-left-icon";
import ShoppingBagIcon from "@/components/icons/shopping-bag-icon";
import PixIcon from "@/components/icons/payment-methods/pix-icon";
import SwitchIcon from "@/components/icons/switch-icon";

interface CheckoutItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

const PaymentReview: React.FC = () => {
  const params = useLocalSearchParams();
  const paymentType = (params.paymentType as string) || "pix";

  // Dados baseados no design Motiff
  const items: CheckoutItem[] = [
    {
      id: "1",
      name: "Mentoria Coletiva sobre Resoluções Jurídicas",
      price: 1580.0,
      quantity: 1
    },
    {
      id: "2",
      name: "Encontro - Comemoração de 10 anos da Cafeeira Garibaldi",
      price: 1200.0,
      quantity: 1
    }
  ];

  const total = 2780.0;

  const handleGenerateCode = () => {
    if (paymentType === "boleto") {
      router.push("/(events)/boleto-payment");
    } else if (paymentType === "mastercard" || paymentType === "visa") {
      router.push(`/(events)/credit-card-form?paymentType=${paymentType}`);
    } else {
      router.push("/(events)/pix-payment");
    }
  };

  const handleBackPress = () => {
    router.back();
  };

  const handleChangePaymentMethod = () => {
    // Navigate to payment method selection
    router.back();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#111828" />

      {/* Status Bar */}
      <View style={styles.statusBar}>
        <View style={{width: 28.43, height: 11.67, marginLeft: 18.78}} />
        <View style={{width: 66.66, height: 11.93, marginTop: 0.17}} />
      </View>

      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <View style={styles.headerTitle}>
          <Text style={styles.headerTitleText}>Revise os dados</Text>
        </View>
      </View>

      {/* Product List Header */}
      <View style={styles.productListHeader}>
        <Text style={styles.productListTitle}>Lista de produtos</Text>
        <Text style={styles.productListCount}>({items.length} itens)</Text>
      </View>

      {/* Product Cards */}
      {items.map((item) => (
        <View key={item.id} style={styles.productCard}>
          <View style={styles.productIconContainer}>
            <ShoppingBagIcon width={20} height={20} replaceColor="#EAECF0" />
          </View>
          <View style={styles.productInfo}>
            <Text style={styles.productName}>{item.name}</Text>
            <View style={styles.productPriceContainer}>
              <Text style={styles.productPrice}>
                R$ {item.price.toFixed(2).replace(".", ",")}
              </Text>
            </View>
          </View>
          <Text style={styles.productQuantity}>{item.quantity}x</Text>
        </View>
      ))}

      {/* Payment Method Section */}
      <View style={styles.paymentMethodSection}>
        <View style={styles.paymentMethodTitle}>
          <Text style={styles.paymentMethodTitleText}>
            Métodos de pagamento
          </Text>
        </View>

        <View style={styles.paymentMethodCard}>
          <View style={styles.paymentMethodIcon}>
            <PixIcon width={34} height={24} />
          </View>
          <View style={styles.paymentMethodInfo}>
            <View style={styles.paymentMethodHeader}>
              <Text style={styles.paymentMethodName}>PIX</Text>
              <Text style={styles.paymentMethodType}>(À Vista)</Text>
            </View>
            <Text style={styles.paymentMethodDescription}>
              Pagamento imediato
            </Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.changePaymentButton}
          onPress={handleChangePaymentMethod}
        >
          <SwitchIcon width={20} height={20} />
          <Text style={styles.changePaymentText}>
            Selecionar outro método de pagamento
          </Text>
        </TouchableOpacity>
      </View>

      {/* Bottom Section */}
      <View style={styles.bottomSection}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Valor a ser pago</Text>
          <Text style={styles.totalValue}>
            R$ {total.toFixed(2).replace(".", ",")}
          </Text>
        </View>

        <TouchableOpacity
          style={styles.generateButton}
          onPress={handleGenerateCode}
        >
          <Text style={styles.generateButtonText}>
            Gerar código de pagamento PIX
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default PaymentReview;
