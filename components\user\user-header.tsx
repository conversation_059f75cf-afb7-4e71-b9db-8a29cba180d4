import {ImageBackground} from "expo-image";
import React from "react";
import {Text, View} from "react-native";
import styles from "../../styles/components/user/user-header.style";

import Button from "../button";
import MessageSendIcon from "../icons/message-send-icon";
import MarkerPinIcon from "../icons/marker-pin-icon";
import Avatar from "./avatar";
import {useTranslation} from "react-i18next";

const UserHeader: React.FC = () => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <ImageBackground
        imageStyle={styles.backgroundImageStyle}
        style={styles.backgroundImage}
        source={require("../../assets/textures/rotated-pattern.png")}
      >
        <View style={styles.overlay} />
        <View style={styles.content}>
          <View style={styles.innerContainer}>
            <Avatar url="https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg" />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>Mozart</Text>
              <View style={styles.locationContainer}>
                <MarkerPinIcon />
                <Text style={styles.locationText}>Plus Ultra - BH</Text>
              </View>
            </View>
            <View style={styles.buttonsContainer}>
              <Button
                style={styles.button}
                text="Iniciar conversa"
                icon={<MessageSendIcon />}
              />
            </View>
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};
export default UserHeader;
