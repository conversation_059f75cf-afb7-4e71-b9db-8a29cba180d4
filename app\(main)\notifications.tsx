import React, {useState, useMemo} from "react";
import {Text, View, ScrollView, TouchableOpacity, Image} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import styles from "@/styles/notifications/notifications.style";
import CalendarIcon from "../../components/icons/calendar-icon";
import AnnounceIcon from "../../components/icons/announce-icon";
import GeneralNotificationsIcon from "../../components/icons/general-notifications-icon";

type NotificationFilter = "all" | "read" | "unread";

interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type: "event" | "survey" | "general";
  avatar?: string;
  actionButton?: {
    text: string;
    onPress: () => void;
  };
}

const Notifications: React.FC = () => {
  const {t} = useTranslation();
  const [activeFilter, setActiveFilter] = useState<NotificationFilter>("all");

  const [notifications] = useState<Notification[]>([
    {
      id: "1",
      title:
        "Phoenix Baker enviou um convite para o evento Encontro de anual de empreendedores em Balneário Camboriú - SC",
      message: "",
      time: "Enviado hoje, às 9:40 AM",
      read: false,
      type: "event",
      avatar:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      actionButton: {
        text: "Ver evento",
        onPress: () => console.log("Ver evento")
      }
    },
    {
      id: "2",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit tristique nascetur sed fusce tellus fames adipiscing lacus neque integer mi bibendum.",
      message: "",
      time: "Enviado terça-feira, às 15:45 AM",
      read: true,
      type: "general"
    },
    {
      id: "3",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit tristique nascetur sed fusce tellus fames adipiscing lacus neque integer mi bibendum.",
      message: "",
      time: "Enviado sexta, às 15:45 AM",
      read: true,
      type: "general"
    },
    {
      id: "4",
      title: "Club M Alphaville publicou uma nova enquete, participe!",
      message: "",
      time: "Enviado hoje, às 15:45 PM",
      read: false,
      type: "survey",
      avatar:
        "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=face",
      actionButton: {
        text: "Ver enquete",
        onPress: () => console.log("Ver enquete")
      }
    },
    {
      id: "5",
      title:
        "Lorem ipsum neque sodales amet porttitor vulputate id purus condimentum suspendisse a blandit tristique nascetur sed fusce tellus fames adipiscing lacus neque integer mi bibendum.",
      message: "",
      time: "Enviado terça-feira, às 15:45 AM",
      read: true,
      type: "general"
    }
  ]);

  const filteredNotifications = useMemo(() => {
    switch (activeFilter) {
      case "read":
        return notifications.filter((n) => n.read);
      case "unread":
        return notifications.filter((n) => !n.read);
      default:
        return notifications;
    }
  }, [notifications, activeFilter]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "event":
        return <CalendarIcon width={20} height={20} />;
      case "survey":
        return <AnnounceIcon width={20} height={20} />;
      default:
        return <GeneralNotificationsIcon width={20} height={20} />;
    }
  };

  const renderFilterTabs = () => (
    <View style={styles.tabContainer}>
      <View style={styles.tabList}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "all" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("all")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "all" && styles.tabButtonTextActive
            ]}
          >
            Todas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "read" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("read")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "read" && styles.tabButtonTextActive
            ]}
          >
            Lidas
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeFilter === "unread" && styles.tabButtonActive
          ]}
          onPress={() => setActiveFilter("unread")}
        >
          <Text
            style={[
              styles.tabButtonText,
              activeFilter === "unread" && styles.tabButtonTextActive
            ]}
          >
            Não lidas
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderNotificationText = (notification: Notification) => {
    if (notification.type === "event" && notification.avatar) {
      // Event notification with avatar - format: "Phoenix Baker enviou um convite para o evento Encontro..."
      const parts = notification.title.split(
        " enviou um convite para o evento "
      );
      if (parts.length === 2) {
        return (
          <Text style={styles.notificationText}>
            <Text style={styles.boldText}>{parts[0]}</Text>
            <Text> enviou um convite para o evento </Text>
            <Text style={styles.boldText}>{parts[1]}</Text>
          </Text>
        );
      }
    } else if (notification.type === "survey") {
      // Survey notification - format: "Club M Alphaville publicou uma nova enquete, participe!"
      const parts = notification.title.split(" publicou");
      if (parts.length === 2) {
        return (
          <Text style={styles.notificationText}>
            <Text style={styles.boldText}>{parts[0]}</Text>
            <Text> publicou{parts[1]}</Text>
          </Text>
        );
      }
    }

    // Default formatting
    return <Text style={styles.notificationText}>{notification.title}</Text>;
  };

  return (
    <ScreenWithHeader
      screenTitle={t("notifications.title", "Notificações")}
      backButton
    >
      <View style={styles.container}>
        {renderFilterTabs()}
        <ScrollView style={styles.notificationsList}>
          {filteredNotifications.map((notification, index) => (
            <View key={notification.id} style={styles.notificationItem}>
              <View style={styles.notificationRow}>
                <View style={styles.avatarContainer}>
                  {notification.avatar ? (
                    <Image
                      source={{uri: notification.avatar}}
                      style={styles.avatar}
                    />
                  ) : (
                    <View style={styles.iconContainer}>
                      {getNotificationIcon(notification.type)}
                    </View>
                  )}
                </View>

                <View style={styles.contentContainer}>
                  {renderNotificationText(notification)}
                </View>

                <View
                  style={
                    notification.read
                      ? styles.readIndicator
                      : styles.unreadIndicator
                  }
                />
              </View>

              {notification.actionButton && (
                <View style={styles.actionButtonContainer}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={notification.actionButton.onPress}
                  >
                    <Text style={styles.actionButtonText}>
                      {notification.actionButton.text}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              <View style={styles.timestampContainer}>
                <Text style={styles.timestampText}>{notification.time}</Text>
              </View>

              {index < filteredNotifications.length - 1 && (
                <View style={styles.separator} />
              )}
            </View>
          ))}

          {filteredNotifications.length === 0 && (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>
                {(() => {
                  if (activeFilter === "all") {
                    return t("notifications.empty", "Nenhuma notificação");
                  } else if (activeFilter === "read") {
                    return t(
                      "notifications.emptyRead",
                      "Nenhuma notificação lida"
                    );
                  } else {
                    return t(
                      "notifications.emptyUnread",
                      "Nenhuma notificação não lida"
                    );
                  }
                })()}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default Notifications;
