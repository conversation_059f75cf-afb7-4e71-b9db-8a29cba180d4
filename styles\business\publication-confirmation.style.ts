import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 24,
    paddingTop: 16
  },
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 16,
    marginBottom: 16
  },
  title: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 28,
    textAlign: "center",
    marginHorizontal: 24,
    marginBottom: 16
  },
  description: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    marginHorizontal: 24,
    marginBottom: 16
  },
  opportunityCard: {
    backgroundColor: "#202938",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#1D2939",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
    marginBottom: 16,
    gap: 12
  },
  opportunityIconContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.20)",
    borderRadius: 50,
    width: 48,
    height: 48,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    padding: 12
  },

  opportunityTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center",
    marginHorizontal: 51
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    gap: 8,
    marginTop: 4
  },
  detailLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1
  },
  detailValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18,
    textAlign: "right"
  },
  paymentSectionTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginTop: 16,
    marginBottom: 8
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 8
  },
  paymentLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  paymentValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18,
    textAlign: "right"
  },
  divider: {
    height: 0.5,
    backgroundColor: "#44465A",
    marginTop: 11.5,
    marginBottom: 12
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    gap: 8
  },
  totalLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    flex: 1
  },
  totalValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "right",
    flex: 1
  },
  buttonContainer: {
    marginTop: 16,
    gap: 10,
    paddingBottom: 20
  },
  secondaryButton: {
    borderRadius: 8,
    paddingHorizontal: 43,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  secondaryButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
