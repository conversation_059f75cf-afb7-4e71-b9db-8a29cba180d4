import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 24
  },
  paymentMethodsHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  paymentMethodsTitle: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  addNewText: {
    color: "#DFE9F0",
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18
  },
  paymentMethodCard: {
    backgroundColor: "#202938",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2
  },
  paymentMethodCardSelected: {
    backgroundColor: "#202938"
  },
  paymentMethodCardWithExpansion: {
    marginBottom: 0,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0
  },
  paymentMethodContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 12
  },
  paymentMethodInfo: {
    flex: 1,
    marginLeft: 12,
    gap: 2
  },
  paymentMethodNameRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginBottom: 2
  },
  paymentMethodName: {
    color: "#FFFFFF",
    fontSize: 11,
    lineHeight: 16
  },
  paymentMethodType: {
    color: "#FFFFFF",
    fontSize: 11,
    fontWeight: "700",
    lineHeight: 16
  },
  paymentMethodDescription: {
    color: "#FFFFFF",
    fontSize: 9,
    lineHeight: 14
  },
  creditCardDescription: {
    flexDirection: "row",
    alignItems: "center",
    gap: 1,
    flexWrap: "wrap"
  },
  creditLabel: {
    color: "#FFFFFF",
    fontSize: 9,
    lineHeight: 14
  },
  cardNumber: {
    color: "#FFFFFF",
    fontSize: 9,
    fontWeight: "700",
    lineHeight: 14
  },
  separator: {
    color: "#FFFFFF",
    fontSize: 9,
    lineHeight: 14
  },
  paymentImmediate: {
    color: "#FFFFFF",
    fontSize: 9,
    lineHeight: 14
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#D0D5DD",
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center"
  },
  checkboxSelected: {
    backgroundColor: "#1F9464",
    borderColor: "#1F9464"
  },
  checkboxInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#FFFFFF"
  },
  bottomSection: {
    backgroundColor: "#111828",
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    paddingHorizontal: 24,
    paddingBottom: 34,
    gap: 16
  },
  summaryTitle: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    textAlign: "center"
  },
  summaryRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 8
  },
  subtotalLabel: {
    color: "#DFE9F0",
    fontSize: 14,
    lineHeight: 20
  },
  itemCount: {
    color: "#DFE9F0",
    fontSize: 14,
    lineHeight: 20
  },
  subtotalValue: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 12
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 4,
    gap: 16
  },
  totalLabel: {
    color: "#DFE9F0",
    fontSize: 16,
    lineHeight: 24,
    flex: 1
  },
  totalValue: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "right",
    flex: 1
  }
});

export default styles;
