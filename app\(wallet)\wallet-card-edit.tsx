import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import InputField from "../../components/input-field";
import styles from "@/styles/wallet/wallet-card-edit.style";

const WalletCardEdit: React.FC = () => {
  const {t} = useTranslation();
  const [formData, setFormData] = useState({
    cardName: "Maria Aparecida dos Santos",
    cardNumber: "1234 5678 9101 1121",
    expiryDate: "12/2029",
    cvv: "987"
  });

  const handleInputChange =
    (field: keyof typeof formData) => (value: string) => {
      setFormData((prev) => ({...prev, [field]: value}));
    };

  const handleSave = () => {
    console.log("Card updated:", formData);
    // Handle card update
  };

  const handleBack = () => {
    // Handle navigation back
    console.log("Navigate back");
  };

  return (
    <ScreenWithHeader
      screenTitle={t("walletCardEdit.title", "Editar cartão")}
      backButton
    >
      <View style={styles.container}>
        <ScrollView style={styles.scrollContainer}>
          {/* Card Image */}
          <View style={styles.cardImageContainer}>
            <View style={styles.cardImage}>
              {/* Using a placeholder for the card image - in real implementation, this would be the SVG from Motiff */}
              <View
                style={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#1A1F71",
                  borderRadius: 12,
                  justifyContent: "center",
                  alignItems: "center"
                }}
              >
                <Text style={{color: "#fff", fontSize: 16, fontWeight: "bold"}}>
                  CARD
                </Text>
              </View>
            </View>
          </View>

          {/* Section Title */}
          <Text style={styles.sectionTitle}>
            {t("walletCardEdit.cardData", "Dados do cartão")}
          </Text>

          {/* Form */}
          <View style={styles.formContainer}>
            {/* Name Field */}
            <InputField
              value={formData.cardName}
              onChangeText={handleInputChange("cardName")}
              placeholder={t(
                "walletCardEdit.cardNamePlaceholder",
                "Nome como está no cartão"
              )}
            />

            {/* Card Number Field */}
            <InputField
              value={formData.cardNumber}
              onChangeText={handleInputChange("cardNumber")}
              placeholder={t(
                "walletCardEdit.cardNumberPlaceholder",
                "Número do cartão"
              )}
            />

            {/* Two Column Layout for Expiry Date and CVV */}
            <View style={styles.twoColumnContainer}>
              <View style={styles.halfWidthField}>
                <InputField
                  value={formData.expiryDate}
                  onChangeText={handleInputChange("expiryDate")}
                  placeholder="MM/AAAA"
                />
              </View>
              <View style={styles.halfWidthField}>
                <InputField
                  value={formData.cvv}
                  onChangeText={handleInputChange("cvv")}
                  placeholder="CVV"
                />
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            {/* Primary Button - Save Information */}
            <TouchableOpacity style={styles.primaryButton} onPress={handleSave}>
              <Text style={styles.primaryButtonText}>
                {t("walletCardEdit.saveInformation", "Salvar informações")}
              </Text>
            </TouchableOpacity>

            {/* Secondary Button - Back */}
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={handleBack}
            >
              <Text style={styles.secondaryButtonText}>
                {t("walletCardEdit.back", "Voltar")}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default WalletCardEdit;
