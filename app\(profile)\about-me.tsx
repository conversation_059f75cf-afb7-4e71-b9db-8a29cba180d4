import React, {use<PERSON><PERSON>back, useMemo, useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
  RadialGradient
} from "react-native-svg";
import UserHeader from "@/components/user/user-header";
import UserTabs from "@/components/user/user-tabs";
import OpportunityCard from "@/components/user/opportunity-card";
import Seals from "@/components/user/seals";

import styles from "@/styles/profile/about-me.style";

// SVG Icons extracted from Motiff design
const EmailIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Path
      d="M26.667 5.333H5.333C3.86 5.333 2.667 6.527 2.667 8v16c0 1.473 1.193 2.667 2.666 2.667h21.334c1.473 0 2.666-1.194 2.666-2.667V8c0-1.473-1.193-2.667-2.666-2.667z"
      fill="#99AAB5"
    />
    <Path
      d="m29.333 8-13.333 8L2.667 8"
      stroke="#FFF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const PhoneIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Path
      d="M29.333 22.56v4a2.667 2.667 0 0 1-2.906 2.667 26.4 26.4 0 0 1-11.507-4.093 26.001 26.001 0 0 1-8-8 26.4 26.4 0 0 1-4.093-11.574 2.667 2.667 0 0 1 2.653-2.893h4a2.667 2.667 0 0 1 2.667 2.293 17.067 17.067 0 0 0 .933 3.747 2.667 2.667 0 0 1-.6 2.813l-1.693 1.694a21.333 21.333 0 0 0 8 8l1.693-1.694a2.667 2.667 0 0 1 2.814-.6 17.067 17.067 0 0 0 3.746.933 2.667 2.667 0 0 1 2.294 2.707z"
      fill="#55ACEE"
    />
  </Svg>
);

const FacebookIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Defs>
      <LinearGradient
        id="facebook-gradient"
        x1="0%"
        y1="0%"
        x2="100%"
        y2="100%"
      >
        <Stop offset="0%" stopColor="#4267B2" />
        <Stop offset="100%" stopColor="#365899" />
      </LinearGradient>
    </Defs>
    <Path
      d="M16 2.667C8.636 2.667 2.667 8.636 2.667 16S8.636 29.333 16 29.333 29.333 23.364 29.333 16 23.364 2.667 16 2.667z"
      fill="url(#facebook-gradient)"
    />
    <Path
      d="M20 10.667h-2.667c-.736 0-1.333.597-1.333 1.333V14h4l-.667 4h-3.333v8h-4v-8H9.333v-4h2.667v-2.667C12 9.194 13.194 8 15.333 8H20v2.667z"
      fill="#FFF"
    />
  </Svg>
);

const TwitterIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Path
      d="M16 2.667C8.636 2.667 2.667 8.636 2.667 16S8.636 29.333 16 29.333 29.333 23.364 29.333 16 23.364 2.667 16 2.667z"
      fill="#FFF"
    />
    <Path
      d="M22.65 8L25.0625 8L17.425 14.0125L26.7625 24L19.8875 24L12.9313 17.9125L4.98125 24L2.5625 24L10.8688 17.2188L2 8L9.1 8L15.3813 13.3063L22.65 8ZM21.1 22.3625L23.5438 22.3625L7.76875 10.5L5.14375 10.5L21.1 22.3625Z"
      fill="#000"
    />
  </Svg>
);

const InstagramIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Defs>
      <RadialGradient id="instagram-gradient" cx="50%" cy="50%" r="50%">
        <Stop offset="0%" stopColor="#F58529" />
        <Stop offset="25%" stopColor="#DD2A7B" />
        <Stop offset="50%" stopColor="#8134AF" />
        <Stop offset="100%" stopColor="#515BD4" />
      </RadialGradient>
    </Defs>
    <Path
      d="M22.667 2.667H9.333C5.651 2.667 2.667 5.651 2.667 9.333v13.334c0 3.682 2.984 6.666 6.666 6.666h13.334c3.682 0 6.666-2.984 6.666-6.666V9.333c0-3.682-2.984-6.666-6.666-6.666z"
      fill="url(#instagram-gradient)"
    />
    <Path
      d="M21.333 15.16a5.333 5.333 0 1 1-10.666 0 5.333 5.333 0 0 1 10.666 0z"
      stroke="#FFF"
      strokeWidth={2}
    />
    <Path
      d="M23.333 8.667h.013"
      stroke="#FFF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const LinkedinIcon: React.FC = () => (
  <Svg width={32} height={32} viewBox="0 0 32 32" fill="none">
    <Path
      d="M26.667 2.667H5.333C3.86 2.667 2.667 3.86 2.667 5.333v21.334c0 1.473 1.193 2.666 2.666 2.666h21.334c1.473 0 2.666-1.193 2.666-2.666V5.333c0-1.473-1.193-2.666-2.666-2.666z"
      fill="#1275B1"
    />
    <Path
      d="M10.667 14.667v10.666M10.667 9.333a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM16 14.667v10.666M16 14.667c0-2.667 2.667-4 5.333-4s5.334 1.333 5.334 4v10.666"
      stroke="#FFF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ArrowIcon: React.FC = () => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill="none">
    <Path
      d="M4.667 11.333L11.333 4.667M11.333 4.667H4.667M11.333 4.667v6.666"
      stroke="#FFF"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

enum UserTabsEnum {
  Opportunities = 1,
  ClaimedBadges = 2,
  AboutMe = 3
}

const AboutMe: React.FC = () => {
  const {t} = useTranslation();
  const [activeTab, setActiveTab] = useState(UserTabsEnum.AboutMe);

  const tabs = [
    {
      title: t("user.opportunities", "Oportunidades"),
      id: UserTabsEnum.Opportunities
    },
    {
      title: t("user.claimedBadges", "Selos e conquistas"),
      id: UserTabsEnum.ClaimedBadges
    },
    {title: t("user.aboutMe", "Sobre mim"), id: UserTabsEnum.AboutMe}
  ];

  const onTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const opportunities = useMemo(() => {
    const list = [];
    for (let i = 0; i < 10; i++) {
      list.push(
        <OpportunityCard
          key={i + 1}
          avatarUrl={
            "https://upload.wikimedia.org/wikipedia/commons/thumb/1/1e/Wolfgang-amadeus-mozart_1.jpg/1200px-Wolfgang-amadeus-mozart_1.jpg"
          }
          userName={"Mozart"}
          createdAt={new Date("2023-10-01T12:00:00Z")}
          description={
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus volutpat imperdiet facilisis. Donec rhoncus lorem lacus, sed ornare nisi imperdiet vel."
          }
          value={1000}
          imageUrl="https://upload.wikimedia.org/wikipedia/commons/0/01/Steinway_Vienna_002.JPG"
        />
      );
    }
    return list;
  }, []);

  const seals = useMemo(() => {
    const list = [];
    for (let i = 0; i < 8; i++) {
      list.push({
        title: `Ouro`
      });
    }
    return list;
  }, []);

  const aboutMeContent = useMemo(
    () => (
      <View>
        {/* Professional Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Perfil profissional</Text>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Especializações</Text>
            <Text style={styles.value}>
              Startup Manager, Especialista em Venture Capital.
            </Text>
          </View>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Áreas de Interesse</Text>
            <Text style={styles.value}>
              Head Growth (Crescimento de Negócios), Análise de Inteligência de
              Mercado.
            </Text>
          </View>

          <View style={styles.profileRow}>
            <Text style={styles.label}>Objetivos</Text>
            <Text style={styles.value}>Estudos de Product Manager.</Text>
          </View>
        </View>

        {/* Contacts Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contatos</Text>

          <View style={styles.contactsGrid}>
            <View style={styles.contactItem}>
              <EmailIcon />
              <View style={styles.contactInfo}>
                <Text style={styles.contactLabel}>E-mail</Text>
                <Text style={styles.contactValue}><EMAIL></Text>
              </View>
            </View>

            <View style={styles.contactItem}>
              <PhoneIcon />
              <View style={styles.contactInfo}>
                <Text style={styles.contactLabel}>Telefone</Text>
                <Text style={styles.contactValue}>+55 66 7777 8888</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Social Networks Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Redes sociais</Text>

          <View style={styles.socialGrid}>
            <View style={styles.socialRow}>
              <View style={styles.socialItem}>
                <FacebookIcon />
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Facebook</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Ver perfil ↗</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.socialItem}>
                <TwitterIcon />
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>X</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Ver perfil ↗</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View style={styles.socialRow}>
              <View style={styles.socialItem}>
                <InstagramIcon />
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Instagram</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Ver perfil ↗</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.socialItem}>
                <LinkedinIcon />
                <View style={styles.socialInfo}>
                  <Text style={styles.socialLabel}>Linkedin</Text>
                  <TouchableOpacity>
                    <Text style={styles.socialLink}>Ver perfil ↗</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    ),
    []
  );

  const content = useMemo(() => {
    switch (activeTab) {
      case UserTabsEnum.Opportunities:
        return <View style={styles.opportunitiesList}>{opportunities}</View>;
      case UserTabsEnum.ClaimedBadges:
        return (
          <View style={styles.objectivesContainer}>
            <Seals seals={seals} />
          </View>
        );
      case UserTabsEnum.AboutMe:
        return aboutMeContent;
      default:
        return aboutMeContent;
    }
  }, [activeTab, opportunities, seals, aboutMeContent]);

  return (
    <ScrollView style={styles.container}>
      <UserHeader />
      <View style={styles.mainContainer}>
        <UserTabs
          tabs={tabs}
          currentTab={activeTab}
          onTabChange={onTabChange}
          style={styles.tabsContainer}
        />
        <View style={styles.contentContainer}>{content}</View>
      </View>
    </ScrollView>
  );
};

export default AboutMe;
