import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 22,
    paddingTop: 0,
    paddingBottom: 24
  },
  contentContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 16,
    paddingTop: 20,
    paddingBottom: 16,
    paddingRight: 8,
    flex: 1,
    marginBottom: 20
  },
  contentHeader: {
    marginBottom: 16
  },
  contentTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center"
  },
  scrollContainer: {
    flex: 1,
    paddingRight: 8
  },
  description: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  disclaimerContainer: {
    marginBottom: 16,
    paddingHorizontal: 2
  },
  disclaimerText: {
    textAlign: "center"
  },
  disclaimerTextNormal: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  disclaimerTextBold: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  buttonContainer: {
    paddingHorizontal: 2
  }
});

export default styles;
