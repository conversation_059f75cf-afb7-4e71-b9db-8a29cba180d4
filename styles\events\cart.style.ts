import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  itemCount: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },
  emptyCartContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 24
  },
  emptyCartIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24
  },
  emptyCartTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: 600,
    lineHeight: 28,
    textAlign: "center",
    marginBottom: 12
  },
  emptyCartText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 32
  },
  cartItemsList: {
    gap: 16,
    marginBottom: 24
  },
  cartItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 9
  },
  itemTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18,
    marginBottom: 8
  },
  itemPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  itemContent: {
    flex: 1,
    marginLeft: 3
  },
  itemActions: {
    gap: 4,
    alignItems: "flex-end"
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center"
  },
  quantityText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24,
    minWidth: 24,
    textAlign: "center"
  },
  summaryContainer: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    paddingTop: 24,
    paddingHorizontal: 24,
    gap: 16
  },
  summaryTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    textAlign: "center"
  },
  summaryRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 8
  },
  summaryLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  summaryValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 12
  },
  totalRow: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 4,
    gap: 16
  },
  totalLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    flex: 1,
    marginTop: 2
  },
  totalValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24,
    textAlign: "right",
    flex: 1
  },
  buttonContainer: {
    marginTop: 4
  },
  itemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: stylesConstants.colors.highlightBackground,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 6,
    marginBottom: 6
  },
  removeButton: {
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    paddingVertical: 2,
    paddingHorizontal: 2
  },
  removeButtonText: {
    color: stylesConstants.colors.error600,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },
  quantityControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  }
});

export default styles;
