import {StyleSheet} from "react-native";
import stylesConstants from "@/styles/styles-constants";

const styles = StyleSheet.create({
  creditCardContainer: {
    width: 196,
    height: 280,
    borderRadius: 16,
    position: "relative",
    overflow: "hidden",
    padding: 24
  },
  logoContainer: {
    position: "absolute",
    top: 24,
    left: 24,
    width: 48,
    height: 30
  },
  cardLogo: {
    width: "100%",
    height: "100%"
  },
  chipContainer: {
    position: "absolute",
    top: 24,
    right: 24,
    width: 36,
    height: 28,
    backgroundColor: "#E5E5E5",
    borderRadius: 6
  },
  cardChip: {
    width: "100%",
    height: "100%"
  },
  nameSection: {
    position: "absolute",
    top: "50%",
    left: 24,
    right: 24,
    transform: [{translateY: -12}]
  },
  cardHolderName: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    color: stylesConstants.colors.fullWhite,
    lineHeight: 24
  },
  bottomSection: {
    position: "absolute",
    bottom: 24,
    left: 24,
    right: 24,
    gap: 4
  },
  cardNumber: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    color: stylesConstants.colors.fullWhite,
    letterSpacing: 1.5,
    lineHeight: 24
  },
  cardFunction: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    color: stylesConstants.colors.fullWhite,
    lineHeight: 18,
    opacity: 0.7
  }
});

export default styles;
