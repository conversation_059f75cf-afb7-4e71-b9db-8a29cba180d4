import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import styles from "@/styles/events/event-sale.style";
import Badge from "../../components/badge";
import AnnounceIcon from "../../components/icons/announce-icon";
import CartPlusIcon from "../../components/cart-plus-icon";
import SimilarEventCard, {
  SimilarEventData
} from "../../components/similar-event-card";
import stylesConstants from "@/styles/styles-constants";

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  address: string;
  organizer: string;
  type: "Online" | "Presencial";
  price: number;
  isFree: boolean;
  attendees: number;
  additionalInfo: string;
}

// Using SimilarEventData interface from the component

const EventSale: React.FC = () => {
  // Mock event data - in real app this would come from API/params
  const [event] = useState<Event>({
    id: "1",
    title: "Encontro anual de empreendedores em Balneário Camboriú - SC",
    description:
      "Lorem ipsum quisque lobortis in eu rhoncus dui nulla lectus sagittis dictum dignissim...",
    date: "14/05/2025",
    time: "19:30 PM - 21:30 PM",
    location: "Expocentro Balneário Camboriú",
    address: "Rua 2300, 300 - Balneário Camboriú, SC",
    organizer: "Sheep Digital Marketing",
    type: "Online", // Changed to Online to match the design
    price: 0.0, // Changed to 0 to show free event
    isFree: true, // Changed to true to show free event version
    attendees: 500,
    additionalInfo: "É necessário o crachá para acessar o local."
  });

  // Similar events data with conditional discount logic
  const similarEvents: SimilarEventData[] = [
    {
      id: "1",
      title: "Workshop de Planejamento e Modelagem de Negócios",
      badges: [
        {
          text: "TECNOLOGIA",
          backgroundColor: stylesConstants.colors.gray700,
          borderColor: stylesConstants.colors.gray700,
          textColor: stylesConstants.colors.fullWhite
        },
        {
          text: "EVENTO VIP",
          backgroundColor: "#F97066",
          borderColor: "#F97066",
          textColor: stylesConstants.colors.fullWhite
        }
      ],
      originalPrice: 1200.0,
      currentPrice: 600.0,
      discountPercentage: 50
    },
    {
      id: "2",
      title: "Masterclass de Liderança e Gestão de Equipes",
      badges: [
        {
          text: "NEGÓCIOS",
          backgroundColor: stylesConstants.colors.brand.primary,
          borderColor: stylesConstants.colors.brand.primary,
          textColor: stylesConstants.colors.fullWhite
        },
        {
          text: "PREMIUM",
          backgroundColor: "#9333EA",
          borderColor: "#9333EA",
          textColor: stylesConstants.colors.fullWhite
        }
      ],
      currentPrice: 400.0
      // No originalPrice = no discount shown
    },
    {
      id: "3",
      title: "Estratégias de Marketing Digital para Pequenas Empresas",
      badges: [
        {
          text: "MARKETING",
          backgroundColor: "#EAB308",
          borderColor: "#EAB308",
          textColor: stylesConstants.colors.fullWhite
        }
      ],
      originalPrice: 600.0,
      currentPrice: 420.0,
      discountPercentage: 30
    }
  ];

  // Badge colors for free event
  const freeBadgeColor = {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderColor: stylesConstants.colors.brand.primary,
    textColor: stylesConstants.colors.fullWhite
  };

  const handlePurchase = () => {
    // Handle purchase logic
    console.log("Purchase event:", event.id);
  };

  const handleAddToCart = () => {
    // Handle add to cart logic
    console.log("Add to cart:", event.id);
  };

  const handleInviteMembers = () => {
    // Handle invite members logic
    console.log("Invite members to event:", event.id);
  };

  const handleGoBack = () => {
    // Handle go back logic
    console.log("Go back");
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.contentContainer}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* Header */}
        <View style={styles.headerContainer}>
          <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
            <Text style={styles.backButtonText}>‹</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Evento</Text>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.cartButton}>
              <CartPlusIcon />
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>1</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Event Card */}
        <View style={styles.eventContentContainer}>
          {/* Event icon centered */}
          <View style={styles.eventIconContainer}>
            <AnnounceIcon
              replaceColor={stylesConstants.colors.fullWhite}
              width={32}
              height={32}
            />
          </View>

          {/* Event title */}
          <Text style={styles.eventTitle}>{event.title}</Text>

          {/* Event description */}
          <View style={styles.eventDescriptionContainer}>
            <Text style={styles.eventDescription}>
              {event.description}{" "}
              <Text style={styles.seeMoreText}>Ver mais</Text>
            </Text>
          </View>

          {/* Free event badge */}
          {event.isFree && (
            <View style={styles.freeEventBadgeContainer}>
              <Badge color={freeBadgeColor} text="EVENTO GRATUITO" />
            </View>
          )}

          {/* Purchase button */}
          <TouchableOpacity
            style={styles.purchaseButton}
            onPress={handlePurchase}
          >
            <Text style={styles.purchaseButtonText}>
              Adquira por: R$ {event.price.toFixed(2).replace(".", ",")}
            </Text>
          </TouchableOpacity>

          {/* Attendees info */}
          <View style={styles.attendeesSection}>
            <Text style={styles.attendeesText}>
              <Text style={styles.attendeesNumber}>
                {event.attendees} pessoas
              </Text>{" "}
              marcaram presença nesse evento.
            </Text>
            <TouchableOpacity
              style={styles.inviteButton}
              onPress={handleInviteMembers}
            >
              <Text style={styles.inviteButtonText}>Convidar membro(s)</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Event Details */}
        <View style={[styles.eventDetailsContainer, styles.eventCard]}>
          <Text style={styles.sectionTitle}>Detalhes do evento</Text>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Sediado por</Text>
            <Text style={styles.eventDetailValue}>{event.organizer}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Tipo de evento</Text>
            <View style={styles.eventTypeContainer}>
              {/* <View style={styles.eventTypeIndicator} /> */}
              <Text style={styles.eventDetailValue}>{event.type}</Text>
            </View>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Data do evento</Text>
            <Text style={styles.eventDetailValue}>{event.date}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Horários do evento</Text>
            <Text style={styles.eventDetailValue}>{event.time}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Local do evento</Text>
            <Text style={styles.eventDetailValue}>{event.location}</Text>
          </View>

          <View style={styles.eventDetailRow}>
            <Text style={styles.eventDetailLabel}>Informações adicionais</Text>
            <Text style={styles.eventDetailValue}>{event.additionalInfo}</Text>
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethodsContainer}>
          <Text style={styles.sectionTitle}>Métodos de pagamento</Text>
          <View style={styles.paymentMethodsGrid}>
            <View style={styles.paymentMethodIcon}>
              <View style={[styles.paymentMethodCard, styles.pixCard]} />
            </View>
            <View style={styles.paymentMethodIcon}>
              <View style={[styles.paymentMethodCard, styles.boletoCard]} />
            </View>
            <View style={styles.paymentMethodIcon}>
              <View style={[styles.paymentMethodCard, styles.visaCard]} />
            </View>
            <View style={styles.paymentMethodIcon}>
              <View style={[styles.paymentMethodCard, styles.mastercardCard]} />
            </View>
            <View style={styles.paymentMethodIcon}>
              <View style={[styles.paymentMethodCard, styles.amexCard]} />
            </View>
          </View>
          <Text style={styles.installmentText}>
            <Text style={styles.installmentLinkText}>
              Opções de parcelamento
            </Text>{" "}
            (parcele em até 12x sem juros).
          </Text>
        </View>

        {/* Similar Events */}
        <View style={styles.similarEventsContainer}>
          <Text style={styles.sectionTitle}>Eventos semelhantes</Text>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.similarEventsScrollContainer}
            style={styles.similarEventsScroll}
          >
            {similarEvents.map((similarEvent) => (
              <SimilarEventCard
                key={similarEvent.id}
                event={similarEvent}
                onPress={(eventId) => {
                  // Handle event card press
                  console.log("Event card pressed:", eventId);
                }}
              />
            ))}
          </ScrollView>
        </View>
      </ScrollView>

      {/* Bottom Purchase Bar */}
      <View style={styles.bottomPurchaseBar}>
        <TouchableOpacity
          style={styles.cartIconButton}
          onPress={handleAddToCart}
        >
          <Text style={styles.cartIconText}>🛒</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.bottomPurchaseButton}
          onPress={handlePurchase}
        >
          <Text style={styles.bottomPurchaseButtonText}>
            {event.isFree
              ? "Comprar agora por R$ 0,00"
              : `Comprar agora por R$ ${event.price
                  .toFixed(2)
                  .replace(".", ",")}`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default EventSale;
