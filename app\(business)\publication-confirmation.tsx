import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {useTranslation} from "react-i18next";
import FullSizeButton from "../../components/full-size-button";
import CheckCircleIcon from "../../components/icons/check-circle-icon";
import LineChartUpIcon from "../../components/icons/line-chart-up-icon";
import styles from "@/styles/business/publication-confirmation.style";

const PublicationConfirmation: React.FC = () => {
  const {t} = useTranslation();

  return (
    <ScreenWithHeader screenTitle="" backButton>
      <ScrollView style={styles.container}>
        {/* Success Icon */}
        <View style={styles.iconContainer}>
          <CheckCircleIcon width={68} height={68} />
        </View>

        {/* Success Message */}
        <Text style={styles.title}>Seu anúncio foi publicado com sucesso!</Text>
        <Text style={styles.description}>
          Sua oportunidade já se encontra disponível para os usuários do
          aplicativo.
        </Text>

        {/* Opportunity Details Card */}
        <View style={styles.opportunityCard}>
          <View style={styles.opportunityIconContainer}>
            <LineChartUpIcon width={24} height={24} color="#EAECF0" />
          </View>

          <Text style={styles.opportunityTitle}>
            [Oportunidade Exclusiva na Praia Brava – Itajaí/SC]
          </Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Publicado por</Text>
            <Text style={styles.detailValue}>Maria Aparecida (Você)</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Data / hora da publicação</Text>
            <Text style={styles.detailValue}>08/06/2025 - 15:45 PM</Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Valor do item</Text>
            <Text style={styles.detailValue}>R$ 150,00</Text>
          </View>
        </View>

        {/* Payment Information Section */}
        <Text style={styles.paymentSectionTitle}>Informações de pagamento</Text>

        <View style={styles.paymentRow}>
          <Text style={styles.paymentLabel}>Método de pagamento</Text>
          <Text style={styles.paymentValue}>PIX</Text>
        </View>

        <View style={styles.paymentRow}>
          <Text style={styles.paymentLabel}>Taxa da publicação</Text>
          <Text style={styles.paymentValue}>R$ 150,00</Text>
        </View>

        <View style={styles.paymentRow}>
          <Text style={styles.paymentLabel}>Subtotal (1 item)</Text>
          <Text style={styles.paymentValue}>R$ 150,00</Text>
        </View>

        <View style={styles.divider} />

        <View style={styles.totalRow}>
          <Text style={styles.totalLabel}>Soma total</Text>
          <Text style={styles.totalValue}>R$ 150,00</Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <FullSizeButton text="Ver publicação" onPress={() => {}} />

          <TouchableOpacity style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>
              Voltar para central de negócios
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PublicationConfirmation;
