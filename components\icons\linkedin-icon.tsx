import React, {useMemo} from "react";
import Svg, {Path, Rect, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface LinkedinIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const LinkedinIcon: React.FC<LinkedinIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#1275B1",
    [props.replaceColor]
  );

  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        d="M16 8A6 6 0 0 1 22 14V21H18V14A2 2 0 0 0 14 14V21H10V9H14V11A6 6 0 0 1 16 8Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Rect
        x={2}
        y={9}
        width={4}
        height={12}
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M4 6A2 2 0 1 0 4 2A2 2 0 0 0 4 6Z"
        stroke={color}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default LinkedinIcon;
