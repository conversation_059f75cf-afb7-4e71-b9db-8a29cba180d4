import React from "react";
import {Text, View, TouchableOpacity} from "react-native";
import Badge from "./badge";
import styles from "@/styles/components/similar-event-card.style";

export interface SimilarEventBadge {
  text: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
}

export interface SimilarEventData {
  id: string;
  title: string;
  badges: SimilarEventBadge[];
  originalPrice?: number;
  currentPrice: number;
  discountPercentage?: number;
}

export interface SimilarEventCardProps {
  event: SimilarEventData;
  onPress?: (eventId: string) => void;
}

const SimilarEventCard: React.FC<SimilarEventCardProps> = ({
  event,
  onPress
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress(event.id);
    }
  };

  const formatPrice = (price: number): string => {
    return price.toFixed(2).replace(".", ",");
  };

  return (
    <View style={styles.container}>
      <View>
        <View style={styles.header}>
          <View style={styles.badges}>
            {event.badges.map((badge, index) => (
              <Badge
                key={`${event.id}-badge-${badge.text}-${index}`}
                color={{
                  backgroundColor: badge.backgroundColor,
                  borderColor: badge.borderColor,
                  textColor: badge.textColor
                }}
                text={badge.text}
              />
            ))}
          </View>
          {/* Only show ECONOMIZE when there's an original price (discount) */}
          {event.originalPrice && event.discountPercentage && (
            <View style={styles.economizeContainer}>
              <View style={styles.economizeIcon}>
                <Text style={styles.economizeIconText}>%</Text>
              </View>
              <Text style={styles.economizeText}>
                ECONOMIZE {event.discountPercentage}%
              </Text>
            </View>
          )}
        </View>

        <Text style={styles.title}>{event.title}</Text>
      </View>

      <View style={styles.pricing}>
        <View style={styles.priceContainer}>
          {/* Only show old price if there's an original price */}
          {event.originalPrice && (
            <View style={styles.oldPriceRow}>
              <Text style={styles.oldPriceLabel}>De:</Text>
              <Text style={styles.oldPrice}>
                R$ {formatPrice(event.originalPrice)}
              </Text>
            </View>
          )}
          <View style={styles.currentPriceRow}>
            <Text style={styles.currentPriceLabel}>Por:</Text>
            <Text style={styles.currentPrice}>
              R$ {formatPrice(event.currentPrice)}
            </Text>
          </View>
        </View>
        <TouchableOpacity style={styles.button} onPress={handlePress}>
          <Text style={styles.buttonText}>VER EVENTO</Text>
          <Text style={styles.buttonArrow}>›</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default SimilarEventCard;
