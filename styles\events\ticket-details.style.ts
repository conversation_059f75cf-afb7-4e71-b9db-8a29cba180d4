import {StyleSheet, Dimensions} from "react-native";
import stylesConstants from "../styles-constants";

const {width} = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  // Header Styles
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 16,
    gap: 60
  },
  backButton: {
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center"
  },
  headerTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  headerSpacer: {
    width: 24,
    height: 24
  },
  // Content Area
  contentArea: {
    flex: 1
  },
  // Scroll Container
  scrollContainer: {
    flex: 1,
    paddingHorizontal: Math.max(24, (width - 327) / 2)
  },
  scrollContentContainer: {
    paddingTop: 20,
    paddingBottom: 20,
    alignItems: "center",
    justifyContent: "flex-start"
  },
  // Ticket Card Styles
  ticketCard: {
    backgroundColor: "transparent",
    marginTop: 8,
    marginBottom: 12,
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center"
  },
  // Background Image
  ticketBackgroundImage: {
    position: "absolute",
    top: 0,
    left: 0,
    width: 327,
    height: 522,
    borderRadius: 11,
    zIndex: 1
  },
  // Ticket Content Overlay
  ticketContentOverlay: {
    position: "relative",
    zIndex: 2,
    width: 327,
    height: 522,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: "center",
    justifyContent: "flex-start"
  },
  eventTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 8,
    paddingHorizontal: 24
  },
  organizerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    marginBottom: 24
  },
  organizerLabel: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  organizerName: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "700",
    lineHeight: 18
  },
  // Details Grid Styles
  detailsGrid: {
    flexDirection: "row",
    marginBottom: 12
  },
  detailColumn: {
    flex: 1,
    paddingHorizontal: 24
  },
  detailColumnFull: {
    flex: 1,
    paddingHorizontal: 0
  },
  detailLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    marginBottom: 8
  },
  detailValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center"
  },
  // QR Code Styles
  qrCodeContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 20,
    marginTop: 4
  },
  qrCodeImage: {
    width: 120,
    height: 120,
    borderRadius: 8,
    marginBottom: 12,
    alignSelf: "center",
    backgroundColor: "#FFFFFF"
  },
  fullScreenButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    alignItems: "center",
    justifyContent: "center"
  },
  fullScreenButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    textDecorationLine: "underline"
  },
  // Bottom Buttons Container
  bottomButtonsContainer: {
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 32,
    gap: 12
  },
  // Button Styles
  unmarkPresenceButton: {
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#FDA29B",
    borderStyle: "solid",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    gap: 6,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  unmarkPresenceButtonText: {
    color: "#B42318",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  closeButton: {
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center"
  },
  closeButtonText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  // QR Modal Styles
  qrModalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: stylesConstants.colors.mainBackground,
    zIndex: 1000
  },
  qrModalContainer: {
    flex: 1,
    paddingTop: 60 // Account for status bar
  },
  qrModalHeader: {
    alignItems: "center",
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault
  },
  qrModalTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 24
  },
  qrModalContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40
  },
  qrModalQRCode: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    padding: 24,
    marginBottom: 32
  },
  qrModalQRCodeImage: {
    width: 250,
    height: 250,
    borderRadius: 8
  },
  qrModalQRCodePlaceholder: {
    width: 250,
    height: 250,
    backgroundColor: stylesConstants.colors.fullBlack,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  qrModalQRCodeText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center"
  },
  qrModalCodeContainer: {
    alignItems: "center"
  },
  qrModalCodeLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 8
  },
  qrModalCodeRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  qrModalCodeValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  },
  copyButton: {
    padding: 8
  },
  copyButtonText: {
    fontSize: 20
  },
  qrModalCloseButton: {
    paddingVertical: 20,
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault
  },
  qrModalCloseButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  }
});

export default styles;
