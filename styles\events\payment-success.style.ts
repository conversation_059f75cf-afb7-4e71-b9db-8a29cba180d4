import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 24
  },
  // Success Header Section
  successContainer: {
    alignItems: "center",
    marginTop: 16,
    marginBottom: 16
  },
  successIcon: {
    width: 76,
    height: 76,
    marginBottom: 16
  },
  title: {
    color: "#DFE9F0",
    fontSize: 18,
    fontWeight: "600",
    lineHeight: 28,
    textAlign: "center",
    marginBottom: 16,
    marginHorizontal: 24
  },
  subtitle: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center",
    marginHorizontal: 24,
    marginBottom: 16
  },
  // Products Section Header
  productsHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 4
  },
  productsTitle: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    width: 158,
    marginTop: 6
  },
  navigationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 68
  },
  navButton: {
    width: 24,
    height: 36,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 2,
    paddingVertical: 8
  },
  navButtonDisabled: {
    opacity: 0.5
  },
  navIcon: {
    width: 20,
    height: 20
  },
  paginationText: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  paginationCurrent: {
    width: 9
  },
  paginationSeparator: {
    width: 6
  },
  paginationTotal: {
    width: 10
  },
  // Product Card
  productCard: {
    backgroundColor: "#202938",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
    marginBottom: 16
  },
  productIconContainer: {
    width: 48,
    height: 48,
    backgroundColor: "rgba(255, 255, 255, 0.20)",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "center",
    marginBottom: 12,
    padding: 12
  },
  productIcon: {
    width: 24,
    height: 24
  },
  productName: {
    color: "#DFE9F0",
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center",
    marginBottom: 16,
    marginHorizontal: 51
  },
  productInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    marginBottom: 16,
    gap: 8
  },
  productInfoLabel: {
    color: "#DFE9F0",
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    flex: 1
  },
  productInfoValue: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 18,
    textAlign: "right",
    flex: 1
  },
  productInfoValueMultiline: {
    minHeight: 36
  },
  priceContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16
  },
  priceLabel: {
    color: "#DFE9F0",
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    width: 143.5
  },
  priceValuesContainer: {
    alignItems: "flex-end"
  },
  originalPrice: {
    color: "#FCFCFD",
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18,
    textDecorationLine: "line-through",
    marginBottom: 2
  },
  discountedPrice: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 18
  },
  // Action Button
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginTop: 12,
    marginHorizontal: 90.5
  },
  actionButtonIcon: {
    width: 20,
    height: 20
  },
  actionButtonText: {
    color: "#FCFCFD",
    fontSize: 14,
    lineHeight: 20
  },
  // Payment Information Section
  paymentInfoTitle: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginTop: 16,
    marginBottom: 8,
    marginHorizontal: 81
  },
  paymentInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
    paddingHorizontal: 24
  },
  paymentInfoLabel: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  },
  paymentInfoValue: {
    color: "#DFE9F0",
    fontSize: 12,
    lineHeight: 18,
    textAlign: "right"
  },
  paymentTotalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "stretch",
    marginTop: 8,
    paddingHorizontal: 24,
    gap: 8
  },
  paymentTotalLabel: {
    color: "#DFE9F0",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    flex: 1
  },
  paymentTotalValue: {
    color: "#DFE9F0",
    fontSize: 16,
    lineHeight: 24,
    textAlign: "right",
    flex: 1
  },
  // Main Action Buttons
  buttonsContainer: {
    gap: 16,
    marginTop: 12,
    marginBottom: 24
  },
  primaryButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#0F7C4D",
    paddingHorizontal: 70.5,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    height: 48,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  primaryButtonText: {
    color: "#DFE9F0",
    fontSize: 16,
    lineHeight: 24
  },
  secondaryButton: {
    borderRadius: 8,
    paddingHorizontal: 87,
    paddingVertical: 12,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  secondaryButtonText: {
    color: "#FCFCFD",
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
