import React from "react";
import Svg, {<PERSON>, SvgProps, Defs, ClipPath} from "react-native-svg";

const MessageSendIcon: React.FC<SvgProps> = (props) => {
  return (
    <Svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <Defs>
        <ClipPath id="clipPath1300561825">
          <Path d="M0 0L20 0L20 20L0 20L0 0Z" fillRule="nonzero" transform="matrix(1 0 0 1 0 0)" />
        </ClipPath>
      </Defs>
      <Path
        clipPath="url(#clipPath1300561825)"
        d="M0.561711 7.73822L6.12503 9.90173L8.28856 15.4651Q8.47054 15.933 8.55221 16.0987Q8.77119 16.5429 9.10857 16.7187Q9.3797 16.8599 9.68541 16.8601Q9.99113 16.8603 10.2624 16.7193Q10.6 16.544 10.8195 16.1Q10.9014 15.9344 11.0839 15.4667L16.5759 1.39357Q16.7423 0.967179 16.7904 0.801589Q16.9195 0.357117 16.8042 0.0120788Q16.7108 -0.26739 16.5025 -0.475739Q16.2942 -0.684085 16.0147 -0.777447Q15.6696 -0.892713 15.2252 -0.763605Q15.0596 -0.715509 14.6332 -0.549107L0.560069 4.94284Q0.0923164 5.12538 -0.0732564 5.20725Q-0.51719 5.42676 -0.692574 5.76435Q-0.833513 6.03563 -0.833333 6.34135Q-0.833153 6.64707 -0.691895 6.91819Q-0.516115 7.25557 -0.071923 7.47456Q0.0937475 7.55623 0.561711 7.73822ZM12.9523 1.89592L1.56453 6.33994L6.56405 8.2842L12.9523 1.89592ZM7.74256 9.46271L14.1308 3.07443L9.68682 14.4622L7.74256 9.46271Z"
        fillRule="evenodd"
        transform="matrix(1 0 0 1 1.98079 1.99209)"
        fill="rgb(251, 252, 252)"
      />
    </Svg>
  );
};

export default MessageSendIcon;
