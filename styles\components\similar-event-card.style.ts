import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    padding: 16,
    width: 320,
    marginRight: 12,
    justifyContent: "space-between",
    minHeight: 146
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
    minHeight: 40
  },
  badges: {
    flexDirection: "row",
    gap: 4,
    alignItems: "flex-start",
    flex: 1,
    flexWrap: "wrap"
  },
  economizeContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  economizeIcon: {
    width: 16,
    height: 16,
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center"
  },
  economizeIconText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "600"
  },
  economizeText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "600"
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 13,
    fontWeight: "600",
    lineHeight: 18,
    marginBottom: 14
  },
  pricing: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end"
  },
  priceContainer: {
    flex: 1,
    gap: 4,
    justifyContent: "flex-end"
  },
  oldPriceRow: {
    flexDirection: "row",
    gap: 2,
    alignItems: "center"
  },
  oldPriceLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  oldPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textDecorationLine: "line-through"
  },
  currentPriceRow: {
    flexDirection: "row",
    gap: 3,
    alignItems: "center"
  },
  currentPriceLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  currentPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20
  },
  button: {
    backgroundColor: "transparent",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 0,
    paddingHorizontal: 0,
    height: 20,
    alignSelf: "flex-end"
  },
  buttonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  buttonArrow: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "400",
    marginLeft: 4,
    lineHeight: 20
  }
});

export default styles;
