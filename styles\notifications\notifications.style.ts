import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  
  // Tab Navigation Styles
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: "#667085",
    marginTop: 16,
    paddingHorizontal: 24
  },
  tabList: {
    flexDirection: "row",
    alignItems: "stretch",
    gap: 16
  },
  tabButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    minHeight: 32,
    paddingTop: 1,
    paddingHorizontal: 28
  },
  tabButtonActive: {
    borderBottomWidth: 2,
    borderBottomColor: stylesConstants.colors.fullWhite
  },
  tabButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "center"
  },
  tabButtonTextActive: {
    fontWeight: "700"
  },

  // Notifications List Styles
  notificationsList: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    paddingBottom: 4
  },
  notificationItem: {
    marginBottom: 8
  },
  notificationRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 16
  },
  
  // Avatar and Icon Styles
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#202938",
    justifyContent: "center",
    alignItems: "center",
    padding: 10
  },
  
  // Content Styles
  contentContainer: {
    flex: 1,
    width: 247
  },
  notificationText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    textAlign: "left",
    minHeight: 40
  },
  boldText: {
    fontWeight: "700"
  },
  
  // Action Button Styles
  actionButtonContainer: {
    marginTop: 16,
    paddingLeft: 56, // Avatar width (40px) + gap (16px)
    alignItems: "flex-start"
  },
  actionButton: {
    backgroundColor: "#0F7C4D",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#0F7C4D",
    paddingHorizontal: 32,
    paddingVertical: 8,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  actionButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    textAlign: "center"
  },
  
  // Timestamp Styles
  timestampContainer: {
    alignItems: "flex-end",
    justifyContent: "flex-end",
    minHeight: 18,
    marginTop: 8
  },
  timestampText: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "right",
    width: 271
  },
  
  // Unread Indicator
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#F97066",
    marginTop: 6
  },
  readIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#98A1B2",
    marginTop: 6
  },
  
  // Separator
  separator: {
    height: 1,
    backgroundColor: "#1D2838",
    marginTop: 8,
    width: "100%"
  },
  
  // Empty State
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40
  },
  emptyStateText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    textAlign: "center"
  }
});

export default styles;
