import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24
  },
  searchInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    gap: 8,
    borderWidth: 1,
    borderColor: "#F2F4F7"
  },
  searchInput: {
    flex: 1,
    color: "#F2F4F7",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20
  },
  categoriesContainer: {
    paddingHorizontal: 24,
    marginBottom: 18
  },
  categoriesTitle: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    width: 67
  },
  categoryIconsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 31.5,
    paddingRight: 15,
    marginBottom: 12
  },
  categoryIconItem: {
    alignItems: "center",
    gap: 4
  },
  categoryIconWrapper: {
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center"
  },
  categoryIconText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center"
  },
  partnersGrid: {
    paddingHorizontal: 24,
    gap: 12
  },
  partnersRow: {
    flexDirection: "row",
    gap: 12
  },
  partnerCard: {
    width: 157,
    height: 64,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  partnerLogoContainer: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    padding: 8
  },
  partnerLogo: {
    width: "100%",
    height: "100%",
    resizeMode: "contain"
  },
  partnerName: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 16,
    textAlign: "center"
  }
});

export default styles;
