import React from "react";
import { render } from "@testing-library/react-native";

jest.mock("react-native-svg", () => {
  const Svg = ({ children, ...rest }: any) => <svg {...rest}>{children}</svg>;
  const Path = (props: any) => <path {...props} />;
  return {
    __esModule: true,
    default: Svg,
    Svg,
    Path
  };
});

import UserXIcon from "../../../components/icons/user-x-icon";

describe("UserXIcon", () => {
  it("renders without crash", () => {
    const { toJSON } = render(<UserXIcon />);
    expect(toJSON()).not.toBeUndefined();
  });

  it("allows custom dimensions", () => {
    const { getByTestId } = render(<UserXIcon width={26} height={26} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon.props.width).toBe(26);
    expect(icon.props.height).toBe(26);
  });

  it("uses default color when no replaceColor is provided", () => {
    const { getByTestId } = render(<UserXIcon testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon).toBeDefined();
  });

  it("uses custom color when replaceColor is provided", () => {
    const customColor = "#FF0000";
    const { getByTestId } = render(<UserXIcon replaceColor={customColor} testID="icon" />);
    const icon = getByTestId("icon");
    expect(icon).toBeDefined();
  });
});
