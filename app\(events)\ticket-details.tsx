import React, {useState} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image
} from "react-native";
import {useLocalSearchParams, router} from "expo-router";
import styles from "@/styles/events/ticket-details.style";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import UserXIcon from "../../components/icons/user-x-icon";

interface Ticket {
  id: string;
  eventTitle: string;
  organizer: string;
  date: string;
  time: string;
  location: string;
  additionalInfo: string;
  ticketCode: string;
}

const TicketDetails: React.FC = () => {
  const {id} = useLocalSearchParams();
  const [showQRModal, setShowQRModal] = useState(false);

  const [ticket] = useState<Ticket>({
    id: (Array.isArray(id) ? id[0] : id) || "1",
    eventTitle: "Mentoria Coletiva sobre Resoluções Jurídicas",
    organizer: "Arbitralis",
    date: "29/05/2025",
    time: "17:30 PM",
    location: "Expocentro Balneário Camboriú - SC",
    additionalInfo: "Nenhuma informação adicional",
    ticketCode: "X9TR-72QK-P5DM"
  });

  const handleCopyTicketCode = (): void => {
    // Using a modern clipboard API alternative
    Alert.alert(
      "Código copiado",
      "O código do ingresso foi copiado para a área de transferência."
    );
  };

  const handleUnmarkPresence = (): void => {
    Alert.alert(
      "Desmarcar presença",
      "Tem certeza que deseja desmarcar sua presença neste evento?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Desmarcar",
          style: "destructive",
          onPress: () => {
            Alert.alert(
              "Presença desmarcada",
              "Sua presença foi desmarcada com sucesso."
            );
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ChevronLeftIcon width={24} height={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalhes do ingresso</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Content Area */}
      <View style={styles.contentArea}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContentContainer}
        >
          {/* Main Ticket Card with Image Background */}
          <View style={styles.ticketCard}>
            {/* Background Image */}
            <Image
              source={require("@/assets/images/ticket-background-empty.png")}
              style={styles.ticketBackgroundImage}
              resizeMode="cover"
            />

            {/* Ticket Content Overlay */}
            <View style={styles.ticketContentOverlay}>
              {/* Event Title */}
              <Text style={styles.eventTitle}>{ticket.eventTitle}</Text>

              {/* Organizer */}
              <View style={styles.organizerContainer}>
                <Text style={styles.organizerLabel}>Sediado por:</Text>
                <Text style={styles.organizerName}>{ticket.organizer}</Text>
              </View>

              {/* Event Details Grid */}
              <View style={styles.detailsGrid}>
                <View style={styles.detailColumn}>
                  <Text style={styles.detailLabel}>Data/horário do evento</Text>
                  <Text style={styles.detailValue}>
                    {ticket.date} - {ticket.time}
                  </Text>
                </View>
                <View style={styles.detailColumn}>
                  <Text style={styles.detailLabel}>Código do ingresso</Text>
                  <Text style={styles.detailValue}>{ticket.ticketCode}</Text>
                </View>
              </View>

              <View style={styles.detailsGrid}>
                <View style={styles.detailColumnFull}>
                  <Text style={styles.detailLabel}>Local do evento</Text>
                  <Text style={styles.detailValue}>{ticket.location}</Text>
                </View>
              </View>

              <View style={styles.detailsGrid}>
                <View style={styles.detailColumnFull}>
                  <Text style={styles.detailLabel}>Informações adicionais</Text>
                  <Text style={styles.detailValue}>
                    {ticket.additionalInfo}
                  </Text>
                </View>
              </View>

              {/* QR Code Section */}
              <View style={styles.qrCodeContainer}>
                <Image
                  source={{
                    uri: "https://static.motiffcontent.com/private/resource/image/197d6b67bc744c5-c1df446e-b5fb-463d-89eb-d56544ceeff6.jpg"
                  }}
                  style={styles.qrCodeImage}
                  resizeMode="cover"
                />
                <TouchableOpacity
                  style={styles.fullScreenButton}
                  onPress={() => setShowQRModal(true)}
                >
                  <Text style={styles.fullScreenButtonText}>
                    Visualizar em tela cheia
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Fixed Bottom Action Buttons */}
        <View style={styles.bottomButtonsContainer}>
          <TouchableOpacity
            style={styles.unmarkPresenceButton}
            onPress={handleUnmarkPresence}
          >
            <UserXIcon width={20} height={20} replaceColor="#B42318" />
            <Text style={styles.unmarkPresenceButtonText}>
              Desmarcar presença
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => router.back()}
          >
            <Text style={styles.closeButtonText}>Fechar</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* QR Code Full Screen Modal */}
      {showQRModal && (
        <View style={styles.qrModalOverlay}>
          <View style={styles.qrModalContainer}>
            <View style={styles.qrModalHeader}>
              <Text style={styles.qrModalTitle}>QR Code</Text>
            </View>

            <View style={styles.qrModalContent}>
              <View style={styles.qrModalQRCode}>
                <Image
                  source={{
                    uri: "https://static.motiffcontent.com/private/resource/image/197d6b67bc744c5-c1df446e-b5fb-463d-89eb-d56544ceeff6.jpg"
                  }}
                  style={styles.qrModalQRCodeImage}
                  resizeMode="cover"
                />
              </View>

              <View style={styles.qrModalCodeContainer}>
                <Text style={styles.qrModalCodeLabel}>Código do ingresso</Text>
                <View style={styles.qrModalCodeRow}>
                  <Text style={styles.qrModalCodeValue}>
                    {ticket.ticketCode}
                  </Text>
                  <TouchableOpacity
                    style={styles.copyButton}
                    onPress={handleCopyTicketCode}
                  >
                    <Text style={styles.copyButtonText}>📋</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={styles.qrModalCloseButton}
              onPress={() => setShowQRModal(false)}
            >
              <Text style={styles.qrModalCloseButtonText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default TicketDetails;
