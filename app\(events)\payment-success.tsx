import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import {router} from "expo-router";
import CheckCircleIcon from "../../components/icons/check-circle-icon";
import ShoppingBagIcon from "../../components/icons/shopping-bag-icon";
import Ticket02Icon from "../../components/icons/ticket-02-icon";
import ChevronLeftIcon from "../../components/icons/chevron-left-icon";
import ChevronRightIcon from "../../components/icons/chevron-right-icon";
import styles from "@/styles/events/payment-success.style";

const PaymentSuccess: React.FC = () => {
  const handleViewEvents = () => {
    router.push("/(tabs)/schedule");
  };

  const handleBackToProducts = () => {
    router.push("/(tabs)/products");
  };

  return (
    <ScreenWithHeader screenTitle="Pagamento efetuado com sucesso!" backButton>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Success Icon and Message */}
        <View style={styles.successContainer}>
          <CheckCircleIcon width={76} height={76} style={styles.successIcon} />

          <Text style={styles.title}>Pagamento efetuado com sucesso!</Text>

          <Text style={styles.subtitle}>
            Enviaremos um e-mail para você com as informações do evento.
          </Text>
        </View>

        {/* Products Header */}
        <View style={styles.productsHeader}>
          <Text style={styles.productsTitle}>Produtos adquiridos</Text>

          <View style={styles.navigationContainer}>
            <TouchableOpacity
              style={[styles.navButton, styles.navButtonDisabled]}
            >
              <ChevronLeftIcon
                width={20}
                height={20}
                style={styles.navIcon}
                stroke="#475466"
              />
            </TouchableOpacity>

            <Text style={[styles.paginationText, styles.paginationCurrent]}>
              1
            </Text>
            <Text style={[styles.paginationText, styles.paginationSeparator]}>
              /
            </Text>
            <Text style={[styles.paginationText, styles.paginationTotal]}>
              2
            </Text>

            <TouchableOpacity style={styles.navButton}>
              <ChevronRightIcon
                width={20}
                height={20}
                style={styles.navIcon}
                stroke="#DFE9F0"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Product Card */}
        <View style={styles.productCard}>
          <View style={styles.productIconContainer}>
            <ShoppingBagIcon
              width={24}
              height={24}
              style={styles.productIcon}
              replaceColor="#EAECF0"
            />
          </View>

          <Text style={styles.productName}>
            Mentoria Coletiva sobre Resoluções Jurídicas
          </Text>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Sediado por</Text>
            <Text style={styles.productInfoValue}>Arbitralis</Text>
          </View>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Local do evento</Text>
            <Text
              style={[
                styles.productInfoValue,
                styles.productInfoValueMultiline
              ]}
            >
              Expocentro Balneário Camboriú - SC
            </Text>
          </View>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Data / hora do evento</Text>
            <Text style={styles.productInfoValue}>08/06/2025 - 15:45 PM</Text>
          </View>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Nº ingresso</Text>
            <Text style={styles.productInfoValue}>556516658419614</Text>
          </View>

          <View style={styles.productInfoRow}>
            <Text style={styles.productInfoLabel}>Qtde. ingressos</Text>
            <Text style={styles.productInfoValue}>1 (um) ingresso(s)</Text>
          </View>

          <View style={styles.priceContainer}>
            <Text style={styles.priceLabel}>Valor do item</Text>
            <View style={styles.priceValuesContainer}>
              <Text style={styles.originalPrice}>R$ 1999,99</Text>
              <Text style={styles.discountedPrice}>R$ 1580,00</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.actionButton}>
            <Ticket02Icon
              width={20}
              height={20}
              style={styles.actionButtonIcon}
            />
            <Text style={styles.actionButtonText}>Ver ingresso</Text>
          </TouchableOpacity>
        </View>

        {/* Payment Information */}
        <Text style={styles.paymentInfoTitle}>Informações de pagamento</Text>

        <View style={styles.paymentInfoRow}>
          <Text style={styles.paymentInfoLabel}>Método de pagamento</Text>
          <Text style={styles.paymentInfoValue}>PIX</Text>
        </View>

        <View style={styles.paymentInfoRow}>
          <Text style={styles.paymentInfoLabel}>Subtotal (2 itens)</Text>
          <Text style={styles.paymentInfoValue}>R$ 3200,00</Text>
        </View>

        <View style={styles.paymentTotalRow}>
          <Text style={styles.paymentTotalLabel}>Soma total</Text>
          <Text style={styles.paymentTotalValue}>R$ 2.780,00</Text>
        </View>

        {/* Buttons */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleViewEvents}
          >
            <Text style={styles.primaryButtonText}>Ver eventos agendados</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleBackToProducts}
          >
            <Text style={styles.secondaryButtonText}>Voltar aos produtos</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScreenWithHeader>
  );
};

export default PaymentSuccess;
