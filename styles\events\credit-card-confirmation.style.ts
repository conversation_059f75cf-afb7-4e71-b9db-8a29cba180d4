import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 16
  },
  headerContainer: {
    marginBottom: 24
  },
  title: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 16
  },
  sectionContainer: {
    marginBottom: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 16
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16
  },
  itemCountText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },
  productCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  productIconContainer: {
    width: 48,
    height: 48,
    backgroundColor: "rgba(255, 255, 255, 0.20)",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
    marginBottom: 8
  },
  productIcon: {
    width: 24,
    height: 24
  },
  productInfo: {
    flex: 1,
    marginLeft: 4,
    gap: 8
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },
  productPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20
  },
  productQuantity: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 700,
    lineHeight: 20,
    marginBottom: 44
  },
  paymentMethodContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#282A2E",
    padding: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  paymentMethodIcon: {
    width: 34,
    height: 24,
    marginTop: 7,
    marginBottom: 7
  },
  paymentMethodInfo: {
    flex: 1,
    gap: 4
  },
  paymentMethodHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4
  },
  paymentMethodType: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18
  },
  paymentMethodInstallments: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 18
  },
  paymentMethodDetails: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingRight: 22
  },
  paymentMethodCredit: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 16
  },
  paymentMethodCardNumber: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 16
  },
  paymentMethodSeparator: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 16
  },
  paymentMethodImmediate: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 16
  },
  changePaymentMethodContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingLeft: 14.5,
    paddingRight: 14.5,
    marginTop: 16
  },
  changePaymentMethodIcon: {
    width: 20,
    height: 20
  },
  changePaymentMethodText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    flex: 1
  },
  bottomContainer: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    gap: 16,
    marginTop: 140
  },
  totalContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
    paddingHorizontal: 24
  },
  totalLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 24
  },
  totalValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24,
    textAlign: "right",
    flex: 1
  },
  paymentButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 24,
    marginTop: 4,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 1
  },
  paymentButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24
  },
  homeIndicatorContainer: {
    alignItems: "center",
    justifyContent: "flex-end",
    minHeight: 34,
    paddingLeft: 121,
    paddingRight: 120,
    paddingBottom: 8
  },
  homeIndicator: {
    width: 134,
    height: 5,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2.5
  }
});

export default styles;
