import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    backgroundColor: stylesConstants.colors.mainBackground,
    flex: 1,
    flexDirection: "column"
  },
  statusBar: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 14.67,
    paddingRight: 14.67,
    paddingTop: 18.07,
    paddingBottom: 9.83
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 16,
    paddingBottom: 16,
    position: "relative"
  },
  backButton: {
    width: 24,
    height: 24,
    position: "absolute",
    left: 0
  },
  headerTitle: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1
  },
  headerTitleText: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center"
  },
  productListHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingLeft: 24,
    paddingRight: 24,
    paddingBottom: 16
  },
  productListTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    width: 122
  },
  productListCount: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    width: 47
  },
  productCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    gap: 8,
    alignItems: "stretch",
    marginLeft: 24,
    marginRight: 24,
    marginBottom: 8,
    flexDirection: "row",
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12
  },
  productIconContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.20)",
    borderRadius: 20,
    padding: 10,
    width: 40,
    justifyContent: "center",
    alignItems: "center",
    height: 40,
    marginTop: 12,
    marginBottom: 12
  },
  productIcon: {
    width: 20,
    height: 20
  },
  productInfo: {
    marginLeft: 4,
    gap: 8,
    flexDirection: "column",
    alignItems: "stretch",
    flex: 1
  },
  productName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "600",
    lineHeight: 18,
    width: 216
  },
  productPriceContainer: {
    width: 216,
    alignItems: "flex-start"
  },
  productPrice: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    width: 73
  },
  productQuantity: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    width: 19,
    marginBottom: 44
  },
  paymentMethodSection: {
    marginLeft: 24,
    marginRight: 24,
    marginTop: 24,
    gap: 16,
    flexDirection: "column",
    alignItems: "stretch"
  },
  paymentMethodTitle: {
    alignItems: "flex-start"
  },
  paymentMethodTitleText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    width: 165
  },
  paymentMethodCard: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderWidth: 1,
    borderColor: "#282A2E",
    borderRadius: 8,
    gap: 12,
    alignItems: "stretch",
    flexDirection: "row",
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    shadowColor: "rgba(16, 24, 40, 0.06)",
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 1,
    shadowRadius: 2,
    elevation: 2
  },
  paymentMethodIcon: {
    width: 34,
    height: 24,
    marginTop: 7,
    marginBottom: 7,
    justifyContent: "center",
    alignItems: "center"
  },
  paymentMethodInfo: {
    gap: 4,
    flexDirection: "column",
    alignItems: "stretch"
  },
  paymentMethodHeader: {
    gap: 4,
    flexDirection: "row",
    alignItems: "stretch"
  },
  paymentMethodName: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    minWidth: 25
  },
  paymentMethodType: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18,
    minWidth: 50
  },
  paymentMethodDescription: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 16,
    width: 97
  },
  changePaymentButton: {
    gap: 8,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    paddingLeft: 14.5,
    paddingRight: 14.5
  },
  changePaymentIcon: {
    width: 20,
    height: 20,
    flexShrink: 0
  },
  changePaymentText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "center",
    flex: 1
  },
  bottomSection: {
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: "#1D2939",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    flexDirection: "column",
    alignItems: "stretch",
    marginTop: "auto",
    gap: 16,
    paddingTop: 20,
    paddingBottom: 34
  },
  totalContainer: {
    gap: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingLeft: 24,
    paddingRight: 24
  },
  totalLabel: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    width: 121
  },
  totalValue: {
    color: "#DFE9F0",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "right",
    width: 190
  },
  generateButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderWidth: 1,
    borderColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    height: 48,
    marginTop: 4,
    marginLeft: 24,
    marginRight: 24,
    paddingLeft: 38,
    paddingRight: 38,
    paddingTop: 12,
    paddingBottom: 12,
    shadowColor: "rgba(16, 24, 40, 0.05)",
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 1,
    shadowRadius: 1,
    elevation: 1
  },
  generateButtonText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center"
  }
});

export default styles;
