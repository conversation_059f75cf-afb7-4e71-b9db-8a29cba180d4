import React, {useMemo} from "react";
import Svg, {Path, SvgProps} from "react-native-svg";
import {ColorValue} from "react-native";

export interface ScalesIconProps extends SvgProps {
  replaceColor?: ColorValue;
}

const ScalesIcon: React.FC<ScalesIconProps> = (props) => {
  const color = useMemo(
    () => props.replaceColor ?? "#FBFCFC",
    [props.replaceColor]
  );

  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      {/* Central pole */}
      <Path
        d="M12 3V21"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
      />
      {/* Base */}
      <Path
        d="M8 21H16"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
      />
      {/* Horizontal beam */}
      <Path
        d="M6 8H18"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
      />
      {/* Left scale */}
      <Path
        d="M3 12C3 10.5 4.5 9 6 9C7.5 9 9 10.5 9 12"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        fill="none"
      />
      {/* Right scale */}
      <Path
        d="M15 12C15 10.5 16.5 9 18 9C19.5 9 21 10.5 21 12"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
        fill="none"
      />
      {/* Left chain */}
      <Path d="M6 8V9" stroke={color} strokeWidth={1.5} strokeLinecap="round" />
      {/* Right chain */}
      <Path
        d="M18 8V9"
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap="round"
      />
    </Svg>
  );
};

export default ScalesIcon;
