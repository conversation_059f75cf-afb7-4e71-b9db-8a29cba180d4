import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 24
  },

  // Card image styles
  cardImageContainer: {
    alignItems: "center",
    marginTop: 6,
    marginBottom: 18
  },
  cardImage: {
    width: 204.58,
    height: 286.58
  },

  // Section title styles
  sectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 18
  },

  // Form styles
  formContainer: {
    gap: 18
  },
  
  // Two column layout for expiry and CVV
  twoColumnContainer: {
    flexDirection: "row",
    gap: 16
  },
  halfWidthField: {
    flex: 1
  },

  // Button styles
  buttonContainer: {
    gap: 4,
    marginTop: 18
  },
  
  primaryButton: {
    backgroundColor: stylesConstants.colors.brand.primary,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: stylesConstants.colors.brand.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 86.5,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  
  primaryButtonText: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 700,
    lineHeight: 24
  },
  
  secondaryButton: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 140,
    alignItems: "center",
    justifyContent: "center",
    height: 48
  },
  
  secondaryButtonText: {
    color: stylesConstants.colors.secondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: 600,
    lineHeight: 24
  }
});

export default styles;
