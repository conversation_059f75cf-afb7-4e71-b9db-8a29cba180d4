import React from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import {useTranslation} from "react-i18next";
import Svg, {<PERSON>, Defs, ClipPath, G} from "react-native-svg";
import styles from "@/styles/wallet/wallet-card-registration.style";

// Icon components using SVG
const UserIcon = () => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    style={styles.inputIcon}
  >
    <Path
      d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const CreditCardIcon = () => (
  <Svg
    width={20}
    height={20}
    viewBox="0 0 24 24"
    fill="none"
    style={styles.inputIcon}
  >
    <Path
      d="M21 4H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M1 10h22"
      stroke="#9CA3AF"
      strokeWidth={2}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ShieldIcon = () => (
  <Svg width={20} height={20} viewBox="0 0 20 20" style={styles.inputIcon}>
    <Defs>
      <ClipPath id="clipPath1500375230">
        <Path d="M0 0L20 0L20 20L0 20L0 0Z" fillRule="nonzero" />
      </ClipPath>
    </Defs>
    <G clipPath="url(#clipPath1500375230)">
      <Path
        d="M2.66667 12.5L7.91667 12.5C8.3769 12.5 8.75 12.1269 8.75 11.6667C8.75 11.2064 8.3769 10.8333 7.91667 10.8333L2.66667 10.8333Q1.88391 10.8333 1.63986 10.8134Q1.3869 10.7927 1.28834 10.7425Q1.04709 10.6196 0.924162 10.3783Q0.87394 10.2798 0.853273 10.0268Q0.833333 9.78275 0.833333 9L0.833333 5L15.8333 5C15.8333 5.46023 16.2064 5.83333 16.6667 5.83333C17.1269 5.83333 17.5 5.46023 17.5 5L17.5 2.66667Q17.5 1.81594 17.4745 1.50414Q17.4271 0.923341 17.2275 0.531693Q16.8587 -0.19207 16.135 -0.560847Q15.7433 -0.760405 15.1625 -0.807859Q14.8507 -0.833333 14 -0.833333L2.66667 -0.833333Q1.81594 -0.833333 1.50414 -0.807859Q0.923341 -0.760405 0.531691 -0.560849Q-0.192073 -0.192074 -0.560849 0.53169Q-0.760405 0.923341 -0.807859 1.50414Q-0.833333 1.81594 -0.833333 2.66667L-0.833333 9Q-0.833333 9.85073 -0.807859 10.1625Q-0.760405 10.7433 -0.56085 11.135Q-0.192075 11.8587 0.53169 12.2275Q0.923341 12.4271 1.50414 12.4745Q1.81594 12.5 2.66667 12.5ZM15.8333 3.33333L0.833333 3.33333L0.833333 2.66667Q0.833333 1.88391 0.853273 1.63986Q0.87394 1.38691 0.924161 1.28834Q1.04709 1.04709 1.28834 0.924161Q1.38691 0.87394 1.63986 0.853273Q1.88391 0.833333 2.66667 0.833333L14 0.833333Q14.7828 0.833333 15.0268 0.853273Q15.2798 0.87394 15.3783 0.924161Q15.6196 1.04709 15.7425 1.28834Q15.7927 1.38691 15.8134 1.63986Q15.8333 1.88391 15.8333 2.66667L15.8333 3.33333ZM12.5162 13.8351Q12.7837 13.9945 12.9748 14.0856Q13.0588 14.1256 13.1495 14.1461Q13.2403 14.1667 13.3333 14.1667Q13.4264 14.1667 13.5171 14.1461Q13.6079 14.1256 13.6919 14.0856Q13.883 13.9945 14.1505 13.8351Q14.6759 13.5221 15.1171 13.1541Q16.6667 11.8615 16.6667 10.3544L16.6667 8.26907Q16.6667 8.20501 16.6569 8.1417Q16.6471 8.07839 16.6277 8.01731Q16.6084 7.95624 16.5799 7.89885Q16.5515 7.84146 16.5146 7.78909Q16.4777 7.73672 16.4332 7.6906Q16.3887 7.64449 16.3377 7.60571Q16.2867 7.56694 16.2304 7.53641Q16.1741 7.50589 16.1137 7.48433L14.2907 6.83293Q13.3328 6.4898 12.3743 6.83315L10.5528 7.48438Q10.4925 7.50595 10.4362 7.53647Q10.3799 7.567 10.3289 7.60578Q10.2779 7.64456 10.2334 7.69067Q10.189 7.73678 10.1521 7.78914Q10.1152 7.84151 10.0867 7.8989Q10.0583 7.95629 10.0389 8.01735Q10.0196 8.07841 10.0098 8.14172Q10 8.20502 10 8.26907L10 10.3544Q10 11.8616 11.5496 13.1541Q11.9908 13.5221 12.5162 13.8351ZM13.3333 12.3818Q12.9422 12.1454 12.6171 11.8742Q11.6667 11.0814 11.6667 10.3544L11.6667 8.85613L12.9364 8.40219Q13.3328 8.26017 13.7299 8.40241L15 8.85624L15 10.3544Q15 11.0814 14.0496 11.8742Q13.7244 12.1454 13.3333 12.3818Z"
        fillRule="evenodd"
        transform="translate(1.66667, 4.16667)"
        fill="#9CA3AF"
      />
    </G>
  </Svg>
);

const WalletCardRegistration: React.FC = () => {
  const {t} = useTranslation();

  const handleSave = () => {
    console.log("Card registration");
    // Handle card registration
  };

  const handleBack = () => {
    // Handle navigation back
    console.log("Navigate back");
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContentContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.contentContainer}>
          {/* Header Indicator */}
          <View style={styles.headerIndicator} />

          {/* Header Title */}
          <Text style={styles.headerTitle}>
            {t("walletCardRegistration.title", "Adicionar novo cartão")}
          </Text>

          {/* Card Image */}
          <View style={styles.cardImageContainer}>
            <View style={styles.cardImage}>
              {/* Chip icon in the top right */}
              <View style={styles.chipIcon} />
            </View>
          </View>

          {/* Section Title */}
          <Text style={styles.sectionTitle}>
            {t("walletCardRegistration.cardData", "Dados do cartão")}
          </Text>

          {/* Form Fields */}
          <View style={styles.formContainer}>
            {/* Name Field */}
            <View style={styles.inputFieldContainer}>
              <View style={styles.inputField}>
                <UserIcon />
                <Text style={styles.inputText}>
                  {t(
                    "walletCardRegistration.cardNamePlaceholder",
                    "Nome do titular"
                  )}
                </Text>
              </View>
            </View>

            {/* Card Number Field */}
            <View style={styles.inputFieldContainer}>
              <View style={styles.inputField}>
                <CreditCardIcon />
                <Text style={styles.inputText}>
                  {t(
                    "walletCardRegistration.cardNumberPlaceholder",
                    "Número do cartão"
                  )}
                </Text>
              </View>
            </View>

            {/* Two Column Layout for Expiry Date and CVV */}
            <View style={styles.twoColumnContainer}>
              <View style={styles.halfWidthField}>
                <View style={styles.inputField}>
                  <CreditCardIcon />
                  <Text style={styles.inputText}>Expira em</Text>
                </View>
              </View>
              <View style={styles.halfWidthField}>
                <View style={styles.inputField}>
                  <ShieldIcon />
                  <Text style={styles.inputText}>CVV</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            {/* Primary Button */}
            <TouchableOpacity style={styles.primaryButton} onPress={handleSave}>
              <Text style={styles.primaryButtonText}>
                {t(
                  "walletCardRegistration.addCard",
                  "Adicionar cartão a carteira"
                )}
              </Text>
            </TouchableOpacity>

            {/* Secondary Button */}
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={handleBack}
            >
              <Text style={styles.secondaryButtonText}>
                {t("walletCardRegistration.back", "Voltar")}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

export default WalletCardRegistration;
