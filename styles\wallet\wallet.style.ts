import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground,
    paddingHorizontal: 24
  },

  // Header styles
  addCardButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "transparent"
  },
  addCardButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 600,
    lineHeight: 18
  },

  // Tab styles
  tabContainer: {
    flexDirection: "row",
    alignItems: "stretch",
    paddingHorizontal: 24,
    marginHorizontal: -24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085",
    gap: 8,
    marginBottom: 24
  },
  tabButton: {
    flex: 1,
    paddingVertical: 1,
    paddingHorizontal: 9.75,
    justifyContent: "center",
    alignItems: "center",
    minHeight: 32
  },
  tabButtonActive: {
    borderBottomWidth: 2,
    borderBottomColor: stylesConstants.colors.fullWhite
  },
  tabButtonText: {
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    lineHeight: 20,
    color: stylesConstants.colors.fullWhite,
    fontWeight: 400
  },
  tabButtonTextActive: {
    fontWeight: 700
  },

  // Carousel styles
  carouselContainer: {
    marginBottom: 18
  },
  scrollView: {
    flexGrow: 0
  },
  scrollViewContent: {
    paddingHorizontal: 24,
    alignItems: "center",
    gap: 16
  },
  carouselWrapper: {
    height: 292,
    marginBottom: 16
  },
  carouselRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    height: 280
  },
  partialCardLeft: {
    width: 55.5,
    overflow: "hidden",
    marginRight: 34
  },
  partialCardRight: {
    width: 55.5,
    overflow: "hidden",
    marginLeft: 34
  },
  partialCardImage: {
    width: 55.5,
    height: 292,
    shadowColor: "#101828",
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 4
  },

  // Navigation styles
  navigationContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 24,
    marginHorizontal: -24,
    gap: 8,
    marginBottom: 16,
    marginTop: 24
  },
  navigationButton: {
    width: 24,
    height: 24
  },
  paginationWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingVertical: 6
  },
  paginationDot: {
    width: 10,
    height: 10,
    backgroundColor: "transparent",
    borderRadius: 5,
    borderWidth: 1,
    borderColor: stylesConstants.colors.fullWhite
  },
  paginationIndicator: {
    width: 40,
    height: 12,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: stylesConstants.colors.fullWhite
  },

  // Card container styles
  cardContainer: {
    width: 196,
    height: 280,
    marginRight: 16
  },
  firstCard: {
    marginLeft: 0
  },
  lastCard: {
    marginRight: 0
  },

  // Member card styles
  memberCard: {
    width: 196,
    height: 280,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "stretch",
    overflow: "hidden"
  },
  cardLeftSection: {
    width: 39,
    backgroundColor: "#055227",
    alignItems: "center",
    justifyContent: "flex-start",
    paddingTop: 12,
    paddingHorizontal: 2.07
  },
  cardLogo: {
    width: 34.87,
    height: 53
  },
  cardRightSection: {
    flex: 1,
    width: 157,
    position: "relative"
  },
  cardBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: "100%",
    height: "100%"
  },
  memberInfoOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 81,
    backgroundColor: "#055227",
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 8,
    gap: 3
  },
  memberName: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20,
    width: 119,
    minHeight: 40
  },
  membershipType: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: 400,
    lineHeight: 18,
    width: 103,
    minHeight: 18
  },

  // Pagination styles
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 8
  },

  // Card Information styles
  cardInfoContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 16,
    paddingBottom: 24,
    borderWidth: 1,
    borderColor: "#282A2E",
    gap: 12,
    marginTop: 18
  },
  cardInfoHeader: {
    flexDirection: "row",
    alignItems: "stretch",
    gap: 12
  },
  cardInfoTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 20,
    flex: 1,
    minHeight: 20
  },
  toggleButton: {
    flexDirection: "row",
    alignItems: "stretch",
    gap: 6,
    backgroundColor: "transparent",
    flexShrink: 0
  },
  toggleButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    width: 62,
    minHeight: 20
  },
  cardInfoContent: {
    gap: 0,
    marginTop: 4
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingVertical: 8
  },
  infoRowAlternate: {
    backgroundColor: "#344054",
    borderRadius: 4,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 8,
    paddingVertical: 8
  },
  infoLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    minHeight: 18
  },
  infoValue: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "right",
    minHeight: 18
  },
  // Specific label widths based on Motiff design
  infoLabelName: {
    flex: 1,
    maxWidth: 118
  },
  infoValueName: {
    flex: 1
  },
  infoLabelFederation: {
    flex: 1,
    maxWidth: 109
  },
  infoValueFederation: {
    flex: 1
  },
  infoLabelAssociation: {
    flex: 1
  },
  infoValueAssociation: {
    flex: 1
  },
  infoLabelActive: {
    flex: 1
  },
  infoValueActive: {
    flex: 1
  }
});

export default styles;
