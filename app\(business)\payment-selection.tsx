import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import <PERSON><PERSON>ithHeader from "../../components/screen-with-header";
import FullSizeButton from "../../components/full-size-button";
import {router, useLocalSearchParams} from "expo-router";
import styles from "@/styles/business/payment-selection.style";
import PixIcon from "../../components/icons/payment-methods/pix-icon";
import VisaIcon from "../../components/icons/payment-methods/visa-icon";
import MastercardIcon from "../../components/icons/payment-methods/mastercard-icon";
import BoletoIcon from "../../components/icons/payment-methods/boleto-icon";
import ExpandablePaymentOptions, {
  PaymentOption
} from "../../components/expandable-payment-options";

interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  type: "pix" | "boleto" | "mastercard" | "visa";
}

const PaymentSelection: React.FC = () => {
  const params = useLocalSearchParams();

  let opportunityData = null;
  try {
    if (params.opportunityData && typeof params.opportunityData === "string") {
      opportunityData = JSON.parse(decodeURIComponent(params.opportunityData));
    }
  } catch (error) {
    console.error("Error parsing opportunity data:", error);
    opportunityData = {};
  }

  const [paymentMethods] = useState<PaymentMethod[]>([
    {
      id: "1",
      name: "PIX",
      description: "Pagamento imediato",
      icon: PixIcon,
      type: "pix"
    },
    {
      id: "2",
      name: "Via Boleto",
      description: "Aprovação em até 8 (oito) horas",
      icon: BoletoIcon,
      type: "boleto"
    },
    {
      id: "3",
      name: "Cartão Mastercard",
      description: "Crédito (com final 9876) - Pagamento imediato",
      icon: MastercardIcon,
      type: "mastercard"
    },
    {
      id: "4",
      name: "Cartão Visa",
      description: "Crédito (com final 1234) - Pagamento imediato",
      icon: VisaIcon,
      type: "visa"
    }
  ]);

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >("2"); // Boleto is selected by default as shown in Motiff design

  const [selectedInstallmentOption, setSelectedInstallmentOption] = useState<
    string | null
  >("2"); // 2x installments selected by default as shown in design

  // Mock data for the opportunity value - matching Motiff design
  const subtotal = 3200.0;
  const total = 2780.0;

  // Installment options for all payment methods
  const installmentOptions: PaymentOption[] = [
    {
      id: "1",
      installments: 1,
      amount: "R$ 2.363,00",
      label: "À Vista"
    },
    {
      id: "2",
      installments: 2,
      amount: "R$ 196,00",
      label: "Sem juros"
    },
    {
      id: "3",
      installments: 3,
      amount: "R$ 196,00",
      label: "Sem juros"
    },
    {
      id: "4",
      installments: 4,
      amount: "R$ 196,00",
      label: "Sem juros"
    },
    {
      id: "5",
      installments: 5,
      amount: "R$ 196,00",
      label: "Sem juros"
    },
    {
      id: "6",
      installments: 6,
      amount: "R$ 196,00",
      label: "Sem juros"
    }
  ];

  const handleContinue = () => {
    if (!selectedPaymentMethod) return;

    const selectedMethod = paymentMethods.find(
      (method) => method.id === selectedPaymentMethod
    );
    if (!selectedMethod) return;

    // Create a serializable version of the payment method (without the icon function)
    const serializablePaymentMethod = {
      id: selectedMethod.id,
      name: selectedMethod.name,
      description: selectedMethod.description,
      type: selectedMethod.type
    };

    const paymentData = {
      paymentMethod: serializablePaymentMethod,
      opportunityData: opportunityData ?? {},
      subtotal,
      total
    };

    try {
      const jsonString = JSON.stringify(paymentData);
      console.log("JSON to encode:", jsonString);
      const encodedData = encodeURIComponent(jsonString);
      console.log("Encoded data:", encodedData);
      router.push(`/(business)/payment-review?paymentData=${encodedData}`);
    } catch (error) {
      console.error("Error encoding payment data:", error);
    }
  };

  return (
    <ScreenWithHeader screenTitle="Pagamento" backButton>
      <View style={styles.container}>
        <ScrollView style={styles.scrollContainer}>
          {/* Payment Methods Header */}
          <View style={styles.paymentMethodsHeader}>
            <Text style={styles.paymentMethodsTitle}>Métodos de pagamento</Text>
            <TouchableOpacity>
              <Text style={styles.addNewText}>Adicionar novo</Text>
            </TouchableOpacity>
          </View>

          {/* Payment Methods */}
          {paymentMethods.map((method) => (
            <View key={method.id}>
              <TouchableOpacity
                style={[
                  styles.paymentMethodCard,
                  selectedPaymentMethod === method.id &&
                    styles.paymentMethodCardSelected,
                  (method.type === "boleto" ||
                    method.type === "mastercard" ||
                    method.type === "visa") &&
                    selectedPaymentMethod === method.id &&
                    styles.paymentMethodCardWithExpansion
                ]}
                onPress={() => setSelectedPaymentMethod(method.id)}
              >
                <View style={styles.paymentMethodContent}>
                  <method.icon width={34} height={24} />

                  <View style={styles.paymentMethodInfo}>
                    <View style={styles.paymentMethodNameRow}>
                      <Text style={styles.paymentMethodName}>
                        {method.name}
                      </Text>
                      <Text style={styles.paymentMethodType}>
                        {method.type === "pix"
                          ? "(À Vista)"
                          : method.type === "boleto"
                          ? "(À Vista / parcelado)"
                          : "(À vista / parcelado)"}
                      </Text>
                    </View>
                    {method.type === "mastercard" || method.type === "visa" ? (
                      <View style={styles.creditCardDescription}>
                        <Text style={styles.creditLabel}>Crédito</Text>
                        <Text style={styles.cardNumber}>
                          (com final{" "}
                          {method.type === "mastercard" ? "9876" : "1234"})
                        </Text>
                        <Text style={styles.separator}>-</Text>
                        <Text style={styles.paymentImmediate}>
                          Pagamento imediato
                        </Text>
                      </View>
                    ) : (
                      <Text style={styles.paymentMethodDescription}>
                        {method.description}
                      </Text>
                    )}
                  </View>
                </View>

                <View
                  style={[
                    styles.checkbox,
                    selectedPaymentMethod === method.id &&
                      styles.checkboxSelected
                  ]}
                >
                  {selectedPaymentMethod === method.id && (
                    <View style={styles.checkboxInner} />
                  )}
                </View>
              </TouchableOpacity>

              {/* Expandable Payment Options for Boleto, Mastercard, and Visa */}
              {(method.type === "boleto" ||
                method.type === "mastercard" ||
                method.type === "visa") && (
                <ExpandablePaymentOptions
                  isVisible={selectedPaymentMethod === method.id}
                  title="Parcelamento"
                  options={installmentOptions}
                  selectedOptionId={selectedInstallmentOption}
                  onOptionSelect={setSelectedInstallmentOption}
                />
              )}
            </View>
          ))}
        </ScrollView>

        {/* Bottom Summary Section */}
        <View style={styles.bottomSection}>
          <Text style={styles.summaryTitle}>Resumo de compra</Text>

          <View style={styles.summaryRow}>
            <Text style={styles.subtotalLabel}>Subtotal</Text>
            <Text style={styles.itemCount}>(2 itens)</Text>
            <Text style={styles.subtotalValue}>R$ 3200,00</Text>
          </View>

          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>Valor total</Text>
            <Text style={styles.totalValue}>R$ 2.780,00</Text>
          </View>

          <FullSizeButton
            text="Selecionar pagamento"
            onPress={handleContinue}
            disabled={!selectedPaymentMethod}
          />
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default PaymentSelection;
